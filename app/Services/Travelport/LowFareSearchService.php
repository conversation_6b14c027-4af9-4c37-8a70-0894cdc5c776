<?php
namespace App\Services\Travelport;

use DOMDocument;

class LowFareSearchService
{
    protected $targetBranch = 'P4620261';
    protected $provider     = '1G';
    protected $credentials  = 'Universal API/uAPI1188273942-7f84622a:9m*LN$5o/k';
    protected $serviceUrl   = 'https://emea.universal-api.travelport.com/B2BGateway/connect/uAPI/AirService';

    public function searchFlights($params)
    {
        $origin      = $params['origin'] ?? 'DXB';
        $destination = $params['destination'] ?? 'CAI';
        $depDate     = $params['dep_date'] ?? now()->addDays(10)->format('Y-m-d');
        $retDate     = $params['return_date'] ?? null;
        $carrier     = $params['carrier'] ?? null;
        $cabinClass  = $params['class'] ?? 'Economy';
        $travelers   = $params['travelers'] ?? ['adult' => '1'];
        $message  = $this->buildSoapMessage($origin, $destination, $depDate, $retDate, $carrier, $cabinClass, $travelers);
        $response = $this->sendSoapRequest($message);

        return $this->parseResponse($response, $message);
    }

    protected function buildSoapMessage($origin, $destination, $depDate, $retDate, $carrier, $cabinClass, $travelers)
    {
        $doc = new DOMDocument('1.0', 'UTF-8');
        $env = $doc->createElementNS('http://schemas.xmlsoap.org/soap/envelope/', 'soapenv:Envelope');
        $env = $doc->appendChild($env);
        $env->appendChild($doc->createElement('soapenv:Header'));

        $body = $doc->createElement('soapenv:Body');
        $env->appendChild($body);

        $req = $doc->createElementNS('http://www.travelport.com/schema/air_v42_0', 'air:LowFareSearchReq');
        $req->setAttribute('AuthorizedBy', 'user');
        $req->setAttribute('TargetBranch', $this->targetBranch);
        $req->setAttribute('SolutionResult', 'true');
        $req->setAttribute('TraceId', 'trace');
        $body->appendChild($req);

        $billing = $doc->createElementNS('http://www.travelport.com/schema/common_v42_0', 'com:BillingPointOfSaleInfo');
        $billing->setAttribute('OriginApplication', 'UAPI');
        $req->appendChild($billing);

        $this->appendFlightLeg($doc, $req, $origin, $destination, $depDate);
        if($retDate) {
            $this->appendFlightLeg($doc, $req, $destination, $origin, $retDate);
        }

        $modifiers = $doc->createElement('air:AirSearchModifiers');
        $req->appendChild($modifiers);

        $providers = $doc->createElement('air:PreferredProviders');
        $provider  = $doc->createElementNS('http://www.travelport.com/schema/common_v42_0', 'com:Provider');
        $provider->setAttribute('Code', $this->provider);
        $providers->appendChild($provider);
        $modifiers->appendChild($providers);

        if ($carrier) {
            $carriers  = $doc->createElement('air:PermittedCarriers');
            $carrierEl = $doc->createElementNS('http://www.travelport.com/schema/common_v42_0', 'com:Carrier');
            $carrierEl->setAttribute('Code', $carrier);
            $carriers->appendChild($carrierEl);
            $modifiers->appendChild($carriers);
        }

        $cabins = $doc->createElement('air:PermittedCabins');
        $cabin  = $doc->createElementNS('http://www.travelport.com/schema/common_v42_0', 'com:CabinClass');
        $cabin->setAttribute('Type', $cabinClass);
        $cabins->appendChild($cabin);
        $modifiers->appendChild($cabins);

        $n = 1;
        // Ensure travelers is an array and has valid data
        if (!is_array($travelers) || empty($travelers)) {
            $travelers = ['adult' => 1]; // Default to 1 adult
        }

        foreach ($travelers as $type => $count) {
            // Skip if count is 0 or invalid
            if (!$count || $count <= 0) {
                continue;
            }

            $code = $type == 'child' ? 'CNN' : ($type == 'infant' ? 'INF' : 'ADT');
            for ($i = 0; $i < (int)$count; $i++) {
                $passenger = $doc->createElementNS('http://www.travelport.com/schema/common_v42_0', 'com:SearchPassenger');
                $passenger->setAttribute('BookingTravelerRef', $n++);
                $passenger->setAttribute('Code', $code);
                if($code == 'CNN') {
                    $passenger->setAttribute('Age', 5);
                }
                $req->appendChild($passenger);
            }
        }

        $doc->formatOutput = true;
        return $doc->saveXML();
    }

    protected function sendSoapRequest($xml)
    {
        $auth = base64_encode($this->credentials);
        $headers = [
            "Content-Type: text/xml;charset=UTF-8",
            "Authorization: Basic $auth",
            "SOAPAction: \"\"",
            "Content-length: " . strlen($xml),
        ];

        $ch = curl_init($this->serviceUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new \Exception('Curl error: ' . curl_error($ch));
        }
        curl_close($ch);

        return $response;
    }

    protected function appendFlightLeg($doc, $parent, $from, $to, $date)
    {
        $leg = $doc->createElement('air:SearchAirLeg');

        $origin = $doc->createElement('air:SearchOrigin');
        $airport = $doc->createElementNS('http://www.travelport.com/schema/common_v42_0', 'com:Airport');
        $airport->setAttribute('Code', $from);
        $origin->appendChild($airport);
        $leg->appendChild($origin);

        $dest = $doc->createElement('air:SearchDestination');
        $airport = $doc->createElementNS('http://www.travelport.com/schema/common_v42_0', 'com:Airport');
        $airport->setAttribute('Code', $to);
        $dest->appendChild($airport);
        $leg->appendChild($dest);

        $time = $doc->createElement('air:SearchDepTime');
        $time->setAttribute('PreferredTime', $date);
        $leg->appendChild($time);

        $parent->appendChild($leg);
    }

    protected function extractPassengerCountsFromRequest($xmlRequest)
    {
        $result = [
            'ADT' => 0,
            'CNN' => 0, // Child
            'INF' => 0  // Infant
        ];
        
        $xml = simplexml_load_string($xmlRequest, 'SimpleXMLElement', LIBXML_NOCDATA, 'soapenv', true);
        if (!$xml) return $result;
    
        $body = $xml->children('soapenv', true)->Body;
        $req = $body->children('air', true)->LowFareSearchReq ?? null;
        if (!$req) return $result;
    
        $common = $req->children('com', true);
        foreach ($common as $passenger) {
            if ($passenger->getName() === 'SearchPassenger') {
                $code = (string) $passenger->attributes()['Code'];
                if (isset($result[$code])) {
                    $result[$code]++;
                }
            }
        }
        
        return $result;
    }

    public function parseResponse($xmlResponse, $xmlRequest = null)
    {
        $results = [];
        $xml = simplexml_load_string($xmlResponse, 'SimpleXMLElement', LIBXML_NOCDATA, 'SOAP', true);
        if (!$xml) return $results;
        // Get passenger counts from request if available
        $passengerCounts = $xmlRequest ? $this->extractPassengerCountsFromRequest($xmlRequest) : [
            'ADT' => 1,
            'CNN' => 0,
            'INF' => 0
        ];
        $body = $xml->children('SOAP', true)->Body;
        $rsp = $body->children('air', true)->LowFareSearchRsp ?? null;
        if (!$rsp) return $results;
    
        // Store segments and fare info
        $segments = [];
        $fareInfoMap = [];

        // Note: BaggageAllowanceInfo is handled through FareInfo references
        // No need for separate baggageMap as baggage info is retrieved via fare references


        foreach ($rsp->children('air', true) as $child) {
            if ($child->getName() === 'AirSegmentList') {
                foreach ($child->children('air', true) as $seg) {
                    $attr = $seg->attributes();
                    $segments[(string)$attr['Key']] = [
                        'key' => (string)$attr['Key'],
                        'carrier' => (string)$attr['Carrier'],
                        'flight_number' => (string)$attr['FlightNumber'],
                        'origin' => (string)$attr['Origin'],
                        'destination' => (string)$attr['Destination'],
                        'departure_time' => (string)$attr['DepartureTime'],
                        'arrival_time' => (string)$attr['ArrivalTime'],
                        'travel_time' => (string)$attr['TravelTime'],
                    ];
                }
            }
    
            if ($child->getName() === 'FareInfoList') {
                foreach ($child->children('air', true) as $info) {
                    $fareKey = (string)$info->attributes()['Key'];
                    $fareInfoMap[$fareKey] = [
                        'fare_basis' => (string)$info->attributes()['FareBasis'],
                        'baggage' => $this->parseBaggageInfo($info)
                    ];
                }
            }
        }
    
        // Process each pricing solution
        foreach ($rsp->children('air', true) as $solution) {
            if ($solution->getName() !== 'AirPricingSolution') continue;
    
            $solutionAttrs = $solution->attributes();
            $solutionData = [
                'total_price' => (string)$solutionAttrs['TotalPrice'],
                'base_price' => (string)$solutionAttrs['BasePrice'],
                'equivalent_base_price' => (string)$solutionAttrs['EquivalentBasePrice'],
                'taxes' => (string)$solutionAttrs['Taxes'],
                'approximate_total_price' => (string)$solutionAttrs['ApproximateTotalPrice'],
                'approximate_base_price' => (string)$solutionAttrs['ApproximateBasePrice'],
                'approximate_taxes' => (string)$solutionAttrs['ApproximateTaxes'],
                'journeys' => [],
                'pricing_info' => [],
                'connections' => [],
                'passenger_counts' => $passengerCounts,
            ];
    
            // Process journeys
            foreach ($solution->children('air', true) as $child) {
                if ($child->getName() === 'Journey') {
                    $journey = [
                        'travel_time' => (string)$child->attributes()['TravelTime'],
                        'segments' => []
                    ];
    
                    foreach ($child->children('air', true) as $segRef) {
                        $segKey = (string)$segRef->attributes()['Key'];
                        if (isset($segments[$segKey])) {
                            $journey['segments'][] = $segments[$segKey];
                        }
                    }
    
                    $solutionData['journeys'][] = $journey;
                }
            }
    
            // Process connections
            foreach ($solution->children('air', true) as $child) {
                if ($child->getName() === 'Connection') {
                    $solutionData['connections'][] = [
                        'segment_index' => (string)$child->attributes()['SegmentIndex']
                    ];
                }
            }
    
            // Process pricing info
            foreach ($solution->children('air', true)->AirPricingInfo as $info) {
                $infoAttrs = $info->attributes();
                $passengerType = $info->children('air', true)->PassengerType;
                $type = (string)($passengerType?->attributes()['Code'] ?? 'ADT');
    
                $bookingInfo = [];
                foreach ($info->children('air', true)->BookingInfo as $booking) {
                    $bookingAttrs = $booking->attributes();
                    $bookingInfo[] = [
                        'booking_code' => (string)$bookingAttrs['BookingCode'],
                        'booking_count' => (string)$bookingAttrs['BookingCount'],
                        'cabin_class' => (string)$bookingAttrs['CabinClass'],
                        'segment_ref' => (string)$bookingAttrs['SegmentRef'],
                    ];
                }
    
                $taxes = [];
                foreach ($info->children('air', true)->TaxInfo as $tax) {
                    $taxAttrs = $tax->attributes();
                    $taxes[] = [
                        'category' => (string)$taxAttrs['Category'],
                        'amount' => (string)$taxAttrs['Amount'],
                    ];
                }
    
                $fareCalc = '';
                $fareCalcNode = $info->children('air', true)->FareCalc;
                if ($fareCalcNode) {
                    $fareCalc = trim((string)$fareCalcNode);
                }
    
                $pricingInfo = [
                    'key' => (string)$infoAttrs['Key'],
                    'passenger_type' => $type,
                    'total_price' => (string)$infoAttrs['TotalPrice'],
                    'base_price' => (string)$infoAttrs['BasePrice'],
                    'equivalent_base_price' => (string)$infoAttrs['EquivalentBasePrice'],
                    'taxes' => (string)$infoAttrs['Taxes'],
                    'approximate_total_price' => (string)$infoAttrs['ApproximateTotalPrice'],
                    'approximate_base_price' => (string)$infoAttrs['ApproximateBasePrice'],
                    'approximate_taxes' => (string)$infoAttrs['ApproximateTaxes'],
                    'latest_ticketing_time' => (string)$infoAttrs['LatestTicketingTime'],
                    'pricing_method' => (string)$infoAttrs['PricingMethod'],
                    'refundable' => (string)$infoAttrs['Refundable'] === 'true',
                    'e_ticketability' => (string)$infoAttrs['ETicketability'],
                    'plating_carrier' => (string)$infoAttrs['PlatingCarrier'],
                    'provider_code' => (string)$infoAttrs['ProviderCode'],
                    'cat35_indicator' => (string)$infoAttrs['Cat35Indicator'] === 'true',
                    'booking_info' => $bookingInfo,
                    'tax_details' => $taxes,
                    'fare_calculation' => $fareCalc,
                    'change_penalty' => $this->parsePenaltyInfo($info->children('air', true)->ChangePenalty),
                    'cancel_penalty' => $this->parsePenaltyInfo($info->children('air', true)->CancelPenalty),
                ];
    
                // Add baggage info from fare references
                // استرجاع مفاتيح الأجرة المرتبطة
                $fareRefs = $info->children('air', true)->FareInfoRef ?? [];
                $baggageInfo = [];
                
                foreach ($fareRefs as $fareRef) {
                    $refKey = (string)$fareRef->attributes()['Key'];
                    if (isset($fareInfoMap[$refKey])) {
                        $bag = $fareInfoMap[$refKey]['baggage'];
                        $desc = $bag['pieces'] 
                            ?: ($bag['weight'] ? "{$bag['weight']} {$bag['unit']}" : '');
                        if ($desc) {
                            $baggageInfo[] = $desc;
                        }
                    }
                }
                $pricingInfo['baggage'] = implode(' / ', array_unique($baggageInfo));


    
                $solutionData['pricing_info'][] = $pricingInfo;
            }
    
            $results[] = $solutionData;
        }
    
        return $results;
    }
    
    protected function parseBaggageInfo($fareInfoNode)
    {
        $baggage = ['pieces' => '', 'weight' => '', 'unit' => ''];
        
        foreach ($fareInfoNode->children('air', true) as $child) {
            if ($child->getName() === 'BaggageAllowance') {
                $baggage['pieces'] = (string)($child->children('air', true)->NumberOfPieces ?? '');
                $weightNode = $child->children('air', true)->MaxWeight ?? null;
                if ($weightNode) {
                    $baggage['weight'] = (string)$weightNode->attributes()['Value'] ?? '';
                    $baggage['unit'] = (string)$weightNode->attributes()['Unit'] ?? '';
                }
            }
        }
        
        return $baggage;
    }

    
    protected function parsePenaltyInfo($penaltyNode)
    {
        if (!$penaltyNode) return null;
        
        $percentage = $penaltyNode->children('air', true)->Percentage;
        $amount = $penaltyNode->children('air', true)->Amount;
        
        return [
            'percentage' => $percentage ? (string)$percentage : null,
            'amount' => $amount ? (string)$amount : null,
        ];
    }
}
