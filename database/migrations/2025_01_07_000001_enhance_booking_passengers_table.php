<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bravo_booking_passengers', function (Blueprint $table) {
            // Title field (MR, MRS, MS)
            if (!Schema::hasColumn('bravo_booking_passengers', 'title')) {
                $table->string('title', 10)->nullable()->after('seat_type');
            }
            
            // Middle name (optional)
            if (!Schema::hasColumn('bravo_booking_passengers', 'middle_name')) {
                $table->string('middle_name', 100)->nullable()->after('first_name');
            }
            
            // Date of birth (enhanced - was datetime, now date)
            if (Schema::hasColumn('bravo_booking_passengers', 'dob')) {
                $table->date('date_of_birth')->nullable()->after('phone');
                // We'll keep the old dob column for now and migrate data later
            } else {
                $table->date('date_of_birth')->nullable()->after('phone');
            }
            
            // Nationality
            if (!Schema::hasColumn('bravo_booking_passengers', 'nationality')) {
                $table->string('nationality', 100)->nullable()->after('date_of_birth');
            }
            
            // Passport information
            if (!Schema::hasColumn('bravo_booking_passengers', 'passport_number')) {
                $table->string('passport_number', 50)->nullable()->after('nationality');
            }
            
            if (!Schema::hasColumn('bravo_booking_passengers', 'passport_nationality')) {
                $table->string('passport_nationality', 100)->nullable()->after('passport_number');
            }
            
            if (!Schema::hasColumn('bravo_booking_passengers', 'passport_expiry')) {
                $table->date('passport_expiry')->nullable()->after('passport_nationality');
            }
            
            // Passenger type (calculated from age)
            if (!Schema::hasColumn('bravo_booking_passengers', 'passenger_type')) {
                $table->enum('passenger_type', ['adult', 'child', 'infant'])->default('adult')->after('passport_expiry');
            }
            
            // Age at time of travel (calculated field)
            if (!Schema::hasColumn('bravo_booking_passengers', 'age_at_travel')) {
                $table->integer('age_at_travel')->nullable()->after('passenger_type');
            }
            
            // Validation flags
            if (!Schema::hasColumn('bravo_booking_passengers', 'passport_valid')) {
                $table->boolean('passport_valid')->default(false)->after('age_at_travel');
            }
            
            if (!Schema::hasColumn('bravo_booking_passengers', 'age_verified')) {
                $table->boolean('age_verified')->default(false)->after('passport_valid');
            }
            
            // Additional contact information
            if (!Schema::hasColumn('bravo_booking_passengers', 'emergency_contact_name')) {
                $table->string('emergency_contact_name', 100)->nullable()->after('age_verified');
            }
            
            if (!Schema::hasColumn('bravo_booking_passengers', 'emergency_contact_phone')) {
                $table->string('emergency_contact_phone', 20)->nullable()->after('emergency_contact_name');
            }
            
            // Special requirements
            if (!Schema::hasColumn('bravo_booking_passengers', 'special_requirements')) {
                $table->text('special_requirements')->nullable()->after('emergency_contact_phone');
            }
            
            // Meal preferences
            if (!Schema::hasColumn('bravo_booking_passengers', 'meal_preference')) {
                $table->string('meal_preference', 50)->nullable()->after('special_requirements');
            }
            
            // Seat preferences
            if (!Schema::hasColumn('bravo_booking_passengers', 'seat_preference')) {
                $table->string('seat_preference', 50)->nullable()->after('meal_preference');
            }
            
            // Frequent flyer information
            if (!Schema::hasColumn('bravo_booking_passengers', 'frequent_flyer_number')) {
                $table->string('frequent_flyer_number', 50)->nullable()->after('seat_preference');
            }
            
            if (!Schema::hasColumn('bravo_booking_passengers', 'frequent_flyer_airline')) {
                $table->string('frequent_flyer_airline', 50)->nullable()->after('frequent_flyer_number');
            }
            
            // Travel document type
            if (!Schema::hasColumn('bravo_booking_passengers', 'travel_document_type')) {
                $table->enum('travel_document_type', ['passport', 'national_id', 'other'])->default('passport')->after('frequent_flyer_airline');
            }
            
            // Travel document image
            if (!Schema::hasColumn('bravo_booking_passengers', 'travel_document_image')) {
                $table->string('travel_document_image', 255)->nullable()->after('travel_document_type');
            }
            
            // Booking reference for this passenger
            if (!Schema::hasColumn('bravo_booking_passengers', 'passenger_reference')) {
                $table->string('passenger_reference', 20)->nullable()->after('travel_document_image');
            }
            
            // Status tracking
            if (!Schema::hasColumn('bravo_booking_passengers', 'status')) {
                $table->enum('status', ['pending', 'confirmed', 'checked_in', 'boarded', 'cancelled'])->default('pending')->after('passenger_reference');
            }
            
            // Timestamps for passenger-specific actions
            if (!Schema::hasColumn('bravo_booking_passengers', 'confirmed_at')) {
                $table->timestamp('confirmed_at')->nullable()->after('status');
            }
            
            if (!Schema::hasColumn('bravo_booking_passengers', 'checked_in_at')) {
                $table->timestamp('checked_in_at')->nullable()->after('confirmed_at');
            }
            
            // Add indexes for better performance
            $table->index(['passenger_type']);
            $table->index(['nationality']);
            $table->index(['passport_number']);
            $table->index(['status']);
            $table->index(['passenger_reference']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bravo_booking_passengers', function (Blueprint $table) {
            $columns = [
                'title', 'middle_name', 'date_of_birth', 'nationality', 'passport_number',
                'passport_nationality', 'passport_expiry', 'passenger_type', 'age_at_travel',
                'passport_valid', 'age_verified', 'emergency_contact_name', 'emergency_contact_phone',
                'special_requirements', 'meal_preference', 'seat_preference', 'frequent_flyer_number',
                'frequent_flyer_airline', 'travel_document_type', 'travel_document_image',
                'passenger_reference', 'status', 'confirmed_at', 'checked_in_at'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('bravo_booking_passengers', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
