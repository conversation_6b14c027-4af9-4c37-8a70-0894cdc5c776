<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('crm_bookings', function (Blueprint $table) {
            // MegaFly booking number (internal reference)
            if (!Schema::hasColumn('crm_bookings', 'megafly_booking_number')) {
                $table->string('megafly_booking_number', 20)->unique()->after('booking_id');
            }
            
            // Airline PNR (Passenger Name Record from airline)
            if (!Schema::hasColumn('crm_bookings', 'airline_pnr')) {
                $table->string('airline_pnr', 20)->nullable()->after('megafly_booking_number');
            }
            
            // GDS PNR (Global Distribution System PNR)
            if (!Schema::hasColumn('crm_bookings', 'gds_pnr')) {
                $table->string('gds_pnr', 20)->nullable()->after('airline_pnr');
            }
            
            // Passenger name (main passenger or group leader)
            if (!Schema::hasColumn('crm_bookings', 'passenger_name')) {
                $table->string('passenger_name', 200)->nullable()->after('customer_name');
            }
            
            // Ticket number / E-ticket
            if (!Schema::hasColumn('crm_bookings', 'ticket_number')) {
                $table->string('ticket_number', 50)->nullable()->after('passenger_name');
            }
            
            // Flight itinerary details (JSON format)
            if (!Schema::hasColumn('crm_bookings', 'itinerary')) {
                $table->json('itinerary')->nullable()->after('ticket_number');
            }
            
            // Price breakdown
            if (!Schema::hasColumn('crm_bookings', 'base_price')) {
                $table->decimal('base_price', 10, 2)->nullable()->after('total');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'taxes')) {
                $table->decimal('taxes', 10, 2)->nullable()->after('base_price');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'fees')) {
                $table->decimal('fees', 10, 2)->nullable()->after('taxes');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'currency')) {
                $table->string('currency', 3)->default('USD')->after('fees');
            }
            
            // Number of passengers breakdown
            if (!Schema::hasColumn('crm_bookings', 'number_of_passengers')) {
                $table->integer('number_of_passengers')->default(1)->after('currency');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'adults_count')) {
                $table->integer('adults_count')->default(1)->after('number_of_passengers');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'children_count')) {
                $table->integer('children_count')->default(0)->after('adults_count');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'infants_count')) {
                $table->integer('infants_count')->default(0)->after('children_count');
            }
            
            // Booking date (when booking was made)
            if (!Schema::hasColumn('crm_bookings', 'booking_date')) {
                $table->timestamp('booking_date')->nullable()->after('infants_count');
            }
            
            // Travel date (departure date)
            if (!Schema::hasColumn('crm_bookings', 'travel_date')) {
                $table->date('travel_date')->nullable()->after('booking_date');
            }
            
            // Return date (for round trip)
            if (!Schema::hasColumn('crm_bookings', 'return_date')) {
                $table->date('return_date')->nullable()->after('travel_date');
            }
            
            // Enhanced status with more options
            if (Schema::hasColumn('crm_bookings', 'status')) {
                // We'll modify the existing status column to support more statuses
                $table->string('status', 20)->change();
            }
            
            // Time limit for booking confirmation
            if (!Schema::hasColumn('crm_bookings', 'time_limit')) {
                $table->timestamp('time_limit')->nullable()->after('return_date');
            }
            
            // Airline information
            if (!Schema::hasColumn('crm_bookings', 'airline_code')) {
                $table->string('airline_code', 10)->nullable()->after('time_limit');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'airline_name')) {
                $table->string('airline_name', 100)->nullable()->after('airline_code');
            }
            
            // Flight information
            if (!Schema::hasColumn('crm_bookings', 'flight_number')) {
                $table->string('flight_number', 20)->nullable()->after('airline_name');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'departure_airport')) {
                $table->string('departure_airport', 10)->nullable()->after('flight_number');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'arrival_airport')) {
                $table->string('arrival_airport', 10)->nullable()->after('departure_airport');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'departure_time')) {
                $table->timestamp('departure_time')->nullable()->after('arrival_airport');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'arrival_time')) {
                $table->timestamp('arrival_time')->nullable()->after('departure_time');
            }
            
            // Booking source and agent information
            if (!Schema::hasColumn('crm_bookings', 'booking_source')) {
                $table->string('booking_source', 50)->default('website')->after('arrival_time');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'agent_id')) {
                $table->unsignedBigInteger('agent_id')->nullable()->after('booking_source');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'agent_name')) {
                $table->string('agent_name', 100)->nullable()->after('agent_id');
            }
            
            // Payment information
            if (!Schema::hasColumn('crm_bookings', 'payment_status')) {
                $table->enum('payment_status', ['pending', 'paid', 'partial', 'refunded', 'failed'])->default('pending')->after('agent_name');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'payment_method')) {
                $table->string('payment_method', 50)->nullable()->after('payment_status');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'payment_reference')) {
                $table->string('payment_reference', 100)->nullable()->after('payment_method');
            }
            
            // Customer communication tracking
            if (!Schema::hasColumn('crm_bookings', 'last_contact_date')) {
                $table->timestamp('last_contact_date')->nullable()->after('payment_reference');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'contact_method')) {
                $table->enum('contact_method', ['email', 'phone', 'sms', 'whatsapp', 'in_person'])->nullable()->after('last_contact_date');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'notes')) {
                $table->text('notes')->nullable()->after('contact_method');
            }
            
            // Trip type
            if (!Schema::hasColumn('crm_bookings', 'trip_type')) {
                $table->enum('trip_type', ['oneway', 'roundtrip', 'multiway'])->default('oneway')->after('notes');
            }
            
            // Booking confirmation status
            if (!Schema::hasColumn('crm_bookings', 'confirmed_at')) {
                $table->timestamp('confirmed_at')->nullable()->after('trip_type');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'cancelled_at')) {
                $table->timestamp('cancelled_at')->nullable()->after('confirmed_at');
            }
            
            if (!Schema::hasColumn('crm_bookings', 'cancellation_reason')) {
                $table->text('cancellation_reason')->nullable()->after('cancelled_at');
            }
            
            // Add indexes for better performance
            $table->index(['megafly_booking_number']);
            $table->index(['airline_pnr']);
            $table->index(['gds_pnr']);
            $table->index(['ticket_number']);
            $table->index(['travel_date']);
            $table->index(['booking_date']);
            $table->index(['payment_status']);
            $table->index(['trip_type']);
            $table->index(['airline_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('crm_bookings', function (Blueprint $table) {
            $columns = [
                'megafly_booking_number', 'airline_pnr', 'gds_pnr', 'passenger_name', 'ticket_number',
                'itinerary', 'base_price', 'taxes', 'fees', 'currency', 'number_of_passengers',
                'adults_count', 'children_count', 'infants_count', 'booking_date', 'travel_date',
                'return_date', 'time_limit', 'airline_code', 'airline_name', 'flight_number',
                'departure_airport', 'arrival_airport', 'departure_time', 'arrival_time',
                'booking_source', 'agent_id', 'agent_name', 'payment_status', 'payment_method',
                'payment_reference', 'last_contact_date', 'contact_method', 'notes', 'trip_type',
                'confirmed_at', 'cancelled_at', 'cancellation_reason'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('crm_bookings', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
