{"__meta": {"id": "Xdee1388df9c87adf952ac7db8e860bbe", "datetime": "2025-07-06 15:25:30", "utime": 1751815530.489672, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[15:25:30] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815530.487654, "collector": "log"}, {"message": "[15:25:30] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815530.487696, "collector": "log"}, {"message": "[15:25:30] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815530.487809, "collector": "log"}, {"message": "[15:25:30] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815530.487824, "collector": "log"}]}, "time": {"start": 1751815530.453372, "end": 1751815530.48968, "duration": 0.03630805015563965, "duration_str": "36.31ms", "measures": [{"label": "Booting", "start": 1751815530.453372, "relative_start": 0, "end": 1751815530.487314, "relative_end": 1751815530.487314, "duration": 0.03394198417663574, "duration_str": "33.94ms", "params": [], "collector": null}, {"label": "Application", "start": 1751815530.48745, "relative_start": 0.03407788276672363, "end": 1751815530.489681, "relative_end": 9.5367431640625e-07, "duration": 0.002231121063232422, "duration_str": "2.23ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2730216, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-55709251 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-55709251\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1373664217 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1373664217\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1106580939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1106580939\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1272593220 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IlRHQ3djRWJkYU9aYW9VQ2Vrd0NTN1E9PSIsInZhbHVlIjoieVJNaDlvVk9UYS9mN0tmUnNaVlFHV09Cd3NmZkVhemhYc0hNRG51NVl4MXZMMWp4c0RDMGdHV0k1cVM5Z1J6MXdpNnMzSkhzL0xsbEJxWWN0VnB1WVBDdnVnclFJQ3FOYks0c3ZkMVFtVzJNbkFXZ1ZWUkx1N2hHN2FmT2FSZ0MiLCJtYWMiOiJmMzgzZDlkZmI4NjM1MmQ3NTkyNzBmYjY1MDliMmYwN2Q0YmFjMjg2YjRiMzg3YjUwNmVkMWM0NzZkODljYjczIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6ImM3QWVSM2I3ME5RbHJmbS80eW13T0E9PSIsInZhbHVlIjoiOWg2cFplMHc3MGZVY3BoZ1B0YmlWZllQUGhXRFdVZ1BnK2VBcmYzc0daRlBuc1pUc0RvdkJwc1RmV2lZQ2NBb1pNWG1oRm0waUM1Rng1RkVpckoyMVVMa3hYWVRZVlFnQ2J1a1NpUTAyLzFlZUcySEl2NG1lT0d3dE1lK1UxNnQiLCJtYWMiOiIyNGVmZjA3MjBhYmZkM2RlYWYwMWE2YzdlNDQ4N2MxMTI3MDRhYTIxMGFmYTMyNjg1MWExMzY2Mzk5MDYxOGFlIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjBNV1JQTzZzVVVmVWhiUExGUDJDV3c9PSIsInZhbHVlIjoicG9Vb3ZTVU8yYk9LcThjRURKTHFzdE80eXRGQVpPdkxXVzcyVGlGaEFrd2dyVzNuQlNuYlNUdkxucjlkbFc5ZG5CeExPSVZvM0p5WDJPZ3ozenJTdVRpQ3ZCVktDK2FvN2NkMS9UbDhqTUZPa05SVzk3d3YzVTRDMlJFQVQ1c2EiLCJtYWMiOiI1NGFkZjA5ZjU4NTNhNDI2NDcwNWM5ZjE1MGZmMmI0M2EzYmU0ZjFhYWQ1ZDk3YzAxOGRkMDMzMjEwM2JiNjUwIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272593220\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-645676808 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55906</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IlRHQ3djRWJkYU9aYW9VQ2Vrd0NTN1E9PSIsInZhbHVlIjoieVJNaDlvVk9UYS9mN0tmUnNaVlFHV09Cd3NmZkVhemhYc0hNRG51NVl4MXZMMWp4c0RDMGdHV0k1cVM5Z1J6MXdpNnMzSkhzL0xsbEJxWWN0VnB1WVBDdnVnclFJQ3FOYks0c3ZkMVFtVzJNbkFXZ1ZWUkx1N2hHN2FmT2FSZ0MiLCJtYWMiOiJmMzgzZDlkZmI4NjM1MmQ3NTkyNzBmYjY1MDliMmYwN2Q0YmFjMjg2YjRiMzg3YjUwNmVkMWM0NzZkODljYjczIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6ImM3QWVSM2I3ME5RbHJmbS80eW13T0E9PSIsInZhbHVlIjoiOWg2cFplMHc3MGZVY3BoZ1B0YmlWZllQUGhXRFdVZ1BnK2VBcmYzc0daRlBuc1pUc0RvdkJwc1RmV2lZQ2NBb1pNWG1oRm0waUM1Rng1RkVpckoyMVVMa3hYWVRZVlFnQ2J1a1NpUTAyLzFlZUcySEl2NG1lT0d3dE1lK1UxNnQiLCJtYWMiOiIyNGVmZjA3MjBhYmZkM2RlYWYwMWE2YzdlNDQ4N2MxMTI3MDRhYTIxMGFmYTMyNjg1MWExMzY2Mzk5MDYxOGFlIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjBNV1JQTzZzVVVmVWhiUExGUDJDV3c9PSIsInZhbHVlIjoicG9Vb3ZTVU8yYk9LcThjRURKTHFzdE80eXRGQVpPdkxXVzcyVGlGaEFrd2dyVzNuQlNuYlNUdkxucjlkbFc5ZG5CeExPSVZvM0p5WDJPZ3ozenJTdVRpQ3ZCVktDK2FvN2NkMS9UbDhqTUZPa05SVzk3d3YzVTRDMlJFQVQ1c2EiLCJtYWMiOiI1NGFkZjA5ZjU4NTNhNDI2NDcwNWM5ZjE1MGZmMmI0M2EzYmU0ZjFhYWQ1ZDk3YzAxOGRkMDMzMjEwM2JiNjUwIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751815530.4534</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751815530</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645676808\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1757986914 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757986914\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-185352712 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 15:25:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkkvOUxZZGRIMHR0ViswSlpCWVpNNHc9PSIsInZhbHVlIjoiTGVzc0NFZWJsOC9OeHVlbFZmTUIyMmlydFo1ekJrNm9nTWdQSDhJU2p3NGllWFkxamhNVjR2bXMxZVFDcXEzVmlGSmFnYUc3cUxhQVJMaytGMld0bGMvY28xa3Q2QTVMaS9XajQ2QkNxeTlWUk1reHd2ZHhmV21GUW1xN0h6S0kiLCJtYWMiOiIyMDgwZTRkNmYzY2UyYTZhNjIzMjkzODU0OTE0MDAzMTViMGY5M2E3OWE2OWViYTBiYWI0NWEzYmE2YmY3ZTRiIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:25:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IklFb0tZSmdUZWVJNUNNVmxjRW5kbHc9PSIsInZhbHVlIjoibWJ0dEl5eUJLNzREV0xRcEl1YWVReUFVZG03Zk96TlU4SjN4NE5WaExRbXpIakZaUjZBWkZaYlRtcmdpeSt0MythUE9MT3FYYUF0bUZpZDExemU2OFg5dk9HdmtseHdjbzl0aXBKazFMSFkwc24zTFA5empWUm5RalVvVGQ4aCsiLCJtYWMiOiI1YjBkZjMwZjQ2NzM3ZjBlODllMjZjMzg2NDg0ZmI3YzRjOGJhZDM5OGE3MDg0NjdkMzYzZWUzNzZjM2NhOTFhIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:25:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkkvOUxZZGRIMHR0ViswSlpCWVpNNHc9PSIsInZhbHVlIjoiTGVzc0NFZWJsOC9OeHVlbFZmTUIyMmlydFo1ekJrNm9nTWdQSDhJU2p3NGllWFkxamhNVjR2bXMxZVFDcXEzVmlGSmFnYUc3cUxhQVJMaytGMld0bGMvY28xa3Q2QTVMaS9XajQ2QkNxeTlWUk1reHd2ZHhmV21GUW1xN0h6S0kiLCJtYWMiOiIyMDgwZTRkNmYzY2UyYTZhNjIzMjkzODU0OTE0MDAzMTViMGY5M2E3OWE2OWViYTBiYWI0NWEzYmE2YmY3ZTRiIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:25:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IklFb0tZSmdUZWVJNUNNVmxjRW5kbHc9PSIsInZhbHVlIjoibWJ0dEl5eUJLNzREV0xRcEl1YWVReUFVZG03Zk96TlU4SjN4NE5WaExRbXpIakZaUjZBWkZaYlRtcmdpeSt0MythUE9MT3FYYUF0bUZpZDExemU2OFg5dk9HdmtseHdjbzl0aXBKazFMSFkwc24zTFA5empWUm5RalVvVGQ4aCsiLCJtYWMiOiI1YjBkZjMwZjQ2NzM3ZjBlODllMjZjMzg2NDg0ZmI3YzRjOGJhZDM5OGE3MDg0NjdkMzYzZWUzNzZjM2NhOTFhIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:25:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185352712\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2128886929 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128886929\", {\"maxDepth\":0})</script>\n"}}