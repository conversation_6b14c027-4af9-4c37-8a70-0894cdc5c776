{"__meta": {"id": "X2ee3d8f64003188113a5905e7eaa5203", "datetime": "2025-07-06 15:27:48", "utime": 1751815668.056428, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[15:27:48] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815668.053866, "collector": "log"}, {"message": "[15:27:48] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815668.053879, "collector": "log"}, {"message": "[15:27:48] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815668.053996, "collector": "log"}, {"message": "[15:27:48] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815668.054009, "collector": "log"}]}, "time": {"start": 1751815668.029949, "end": 1751815668.056437, "duration": 0.026488065719604492, "duration_str": "26.49ms", "measures": [{"label": "Booting", "start": 1751815668.029949, "relative_start": 0, "end": 1751815668.053515, "relative_end": 1751815668.053515, "duration": 0.023566007614135742, "duration_str": "23.57ms", "params": [], "collector": null}, {"label": "Application", "start": 1751815668.053659, "relative_start": 0.023710012435913086, "end": 1751815668.056437, "relative_end": 0, "duration": 0.0027780532836914062, "duration_str": "2.78ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2730216, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-978127867 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-978127867\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1470788493 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1470788493\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-18098727 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-18098727\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-571987736 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IjloYkkwd1FsRGFWZmRzNElRMXUvNUE9PSIsInZhbHVlIjoiM244MWhySTlWQ1NXWk10UEJMSVdvdGUyektiNHM1cElvNi84UzdxVUZDRFYyenZrZkRnKzB1WnFYQ0N0U0tueUdiWDdoM29wZXVORUFzSDFlMXdCSlRidHNaWkZtOFJiQi9ZcFJPUDF1U3FpbjJJV2V2eUtQbFZXZm4xYm1XalMiLCJtYWMiOiJlM2ZmNTNkOWMwODY1MWY5MmM0MDM1ZjhmNTA4ZmQ1MzU3ODdjOWNmZjdhZjFhNTgwMDgyMmY2NGRjZTI0Yjc4IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjFEVVFxSURud3M3SlJJaTlwakQwbVE9PSIsInZhbHVlIjoiUmNrQkRXN2d1SjZIQTZGQWRzaklXVTNFQUNDRjE4SW5nSkx4MTcya2hFbjZLL0k4UC9EYWxreGVZVWptVSthdHF4TDg5bTZxMlFqalg2S1pQRldycXVmNEpCdzU3RDhmbW5sSUZ3Vy9qUW5Fcm1adEFzZ2d4dUpDV0kybEc5UlkiLCJtYWMiOiIzNGU2NzQzMjU0YzdlNWVlMmVhMzg4NjNmZmVkNWVhNWQ4NDY2MWM5NTU3NTNiMDJjNDM0NDMwNjM3YjBmNDg1IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjBNV1JQTzZzVVVmVWhiUExGUDJDV3c9PSIsInZhbHVlIjoicG9Vb3ZTVU8yYk9LcThjRURKTHFzdE80eXRGQVpPdkxXVzcyVGlGaEFrd2dyVzNuQlNuYlNUdkxucjlkbFc5ZG5CeExPSVZvM0p5WDJPZ3ozenJTdVRpQ3ZCVktDK2FvN2NkMS9UbDhqTUZPa05SVzk3d3YzVTRDMlJFQVQ1c2EiLCJtYWMiOiI1NGFkZjA5ZjU4NTNhNDI2NDcwNWM5ZjE1MGZmMmI0M2EzYmU0ZjFhYWQ1ZDk3YzAxOGRkMDMzMjEwM2JiNjUwIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571987736\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1201119966 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">56616</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IjloYkkwd1FsRGFWZmRzNElRMXUvNUE9PSIsInZhbHVlIjoiM244MWhySTlWQ1NXWk10UEJMSVdvdGUyektiNHM1cElvNi84UzdxVUZDRFYyenZrZkRnKzB1WnFYQ0N0U0tueUdiWDdoM29wZXVORUFzSDFlMXdCSlRidHNaWkZtOFJiQi9ZcFJPUDF1U3FpbjJJV2V2eUtQbFZXZm4xYm1XalMiLCJtYWMiOiJlM2ZmNTNkOWMwODY1MWY5MmM0MDM1ZjhmNTA4ZmQ1MzU3ODdjOWNmZjdhZjFhNTgwMDgyMmY2NGRjZTI0Yjc4IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjFEVVFxSURud3M3SlJJaTlwakQwbVE9PSIsInZhbHVlIjoiUmNrQkRXN2d1SjZIQTZGQWRzaklXVTNFQUNDRjE4SW5nSkx4MTcya2hFbjZLL0k4UC9EYWxreGVZVWptVSthdHF4TDg5bTZxMlFqalg2S1pQRldycXVmNEpCdzU3RDhmbW5sSUZ3Vy9qUW5Fcm1adEFzZ2d4dUpDV0kybEc5UlkiLCJtYWMiOiIzNGU2NzQzMjU0YzdlNWVlMmVhMzg4NjNmZmVkNWVhNWQ4NDY2MWM5NTU3NTNiMDJjNDM0NDMwNjM3YjBmNDg1IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjBNV1JQTzZzVVVmVWhiUExGUDJDV3c9PSIsInZhbHVlIjoicG9Vb3ZTVU8yYk9LcThjRURKTHFzdE80eXRGQVpPdkxXVzcyVGlGaEFrd2dyVzNuQlNuYlNUdkxucjlkbFc5ZG5CeExPSVZvM0p5WDJPZ3ozenJTdVRpQ3ZCVktDK2FvN2NkMS9UbDhqTUZPa05SVzk3d3YzVTRDMlJFQVQ1c2EiLCJtYWMiOiI1NGFkZjA5ZjU4NTNhNDI2NDcwNWM5ZjE1MGZmMmI0M2EzYmU0ZjFhYWQ1ZDk3YzAxOGRkMDMzMjEwM2JiNjUwIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751815668.0299</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751815668</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1201119966\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-512997732 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512997732\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1751604823 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 15:27:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlVKZVZQWDY0ZHExbVliOXVCalpoS2c9PSIsInZhbHVlIjoiQmZON1dTTjZmQ01tVmhXc2YwSGRSKy80N0E5WGZ0ZXNBc1RsQXFtWXQzZzBqVk8wcnZoNW5IbFh2S2p6RG1DeTVaUmlNYkdjeFVVcVpOa2RyckdMNVdvc0ZHK0cxdUhjRDVEbmN4RWM5aEtkeE96ZXQza2M4eEVubU1ndisyVFYiLCJtYWMiOiJiYTNhMmNiNDlkNWMyM2FjNWRkOWEwODU4M2Q4NzFkM2VmZjcyZWI4MTE0MzFmNjhmYjFiMjJiZGU2ODJlNWZkIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:27:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6InpOcndaSGdDV1lac2laYzg5UzNvNVE9PSIsInZhbHVlIjoieUZhbG1WK3VYenA5MnJ4a01QOUs5RkN2Y1BZWDRpUEhEM0pmRnRER1ZNMld4U1lSOWZ2RHU2VkRPT2FZYlllUUppYW96UEtJSHVBSUorUVhCQmYzdm8wT2ZGemJvNzZ0SnBScjZhUi9Xdi94STVscXp4amtVQVQrSGk3SlBYci8iLCJtYWMiOiIyZDExMzQxZGEwNzI0YjIyYzBmZWRjZGU4MDg3YTY3OThkYTg0YjU3MzI5YWVkN2QxNWE1ZTY5YzBkNWRiNDI3IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:27:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlVKZVZQWDY0ZHExbVliOXVCalpoS2c9PSIsInZhbHVlIjoiQmZON1dTTjZmQ01tVmhXc2YwSGRSKy80N0E5WGZ0ZXNBc1RsQXFtWXQzZzBqVk8wcnZoNW5IbFh2S2p6RG1DeTVaUmlNYkdjeFVVcVpOa2RyckdMNVdvc0ZHK0cxdUhjRDVEbmN4RWM5aEtkeE96ZXQza2M4eEVubU1ndisyVFYiLCJtYWMiOiJiYTNhMmNiNDlkNWMyM2FjNWRkOWEwODU4M2Q4NzFkM2VmZjcyZWI4MTE0MzFmNjhmYjFiMjJiZGU2ODJlNWZkIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:27:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6InpOcndaSGdDV1lac2laYzg5UzNvNVE9PSIsInZhbHVlIjoieUZhbG1WK3VYenA5MnJ4a01QOUs5RkN2Y1BZWDRpUEhEM0pmRnRER1ZNMld4U1lSOWZ2RHU2VkRPT2FZYlllUUppYW96UEtJSHVBSUorUVhCQmYzdm8wT2ZGemJvNzZ0SnBScjZhUi9Xdi94STVscXp4amtVQVQrSGk3SlBYci8iLCJtYWMiOiIyZDExMzQxZGEwNzI0YjIyYzBmZWRjZGU4MDg3YTY3OThkYTg0YjU3MzI5YWVkN2QxNWE1ZTY5YzBkNWRiNDI3IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:27:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751604823\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-787723220 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787723220\", {\"maxDepth\":0})</script>\n"}}