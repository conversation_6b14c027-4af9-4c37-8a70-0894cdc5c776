{"__meta": {"id": "X7cb3a2e7990a789e0002a6d0d14cbfac", "datetime": "2025-07-06 14:28:03", "utime": 1751812083.390812, "method": "GET", "uri": "/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:28:03] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812083.377225, "collector": "log"}, {"message": "[14:28:03] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812083.377315, "collector": "log"}, {"message": "[14:28:03] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812083.377929, "collector": "log"}, {"message": "[14:28:03] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812083.378007, "collector": "log"}]}, "time": {"start": 1751812083.334169, "end": 1751812083.390829, "duration": 0.05666017532348633, "duration_str": "56.66ms", "measures": [{"label": "Booting", "start": 1751812083.334169, "relative_start": 0, "end": 1751812083.376443, "relative_end": 1751812083.376443, "duration": 0.04227399826049805, "duration_str": "42.27ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812083.376665, "relative_start": 0.0424962043762207, "end": 1751812083.39083, "relative_end": 9.5367431640625e-07, "duration": 0.014164924621582031, "duration_str": "14.16ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3472304, "peak_usage_str": "3MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException", "message": "The route uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg could not be found.", "code": 0, "file": "/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php", "line": 44, "stack_trace": "#0 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)\n#1 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/Router.php(753): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))\n#2 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))\n#3 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))\n#4 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))\n#5 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))\n#6 /Users/<USER>/Projects/Mega-Fly/themes/Base/Core/Middleware/RunUpdater.php(72): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#7 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Themes\\Base\\Core\\Middleware\\RunUpdater->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#8 /Users/<USER>/Projects/Mega-Fly/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#9 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#10 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#11 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#12 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#13 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#14 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#15 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#16 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#17 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#18 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#19 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#20 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#21 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#22 /Users/<USER>/Projects/Mega-Fly/app/Http/Middleware/RedirectToInstaller.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#23 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): App\\Http\\Middleware\\RedirectToInstaller->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#24 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#25 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#26 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#27 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))\n#28 /Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))\n#29 /Users/<USER>/Projects/Mega-Fly/public/index.php(71): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))\n#30 /Users/<USER>/Projects/Mega-Fly/server.php(21): require_once('/Users/<USER>')\n#31 {main}", "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"105 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>162</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">handleMatchedRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Routing\\AbstractRouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a><samp data-depth=4 id=sf-dump-**********-ref246 class=sf-dump-compact>\n        +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#48</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#47</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#54</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#50</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:26</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n            \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n            \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57975</span>\"\n            \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n            \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n            \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n            \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n            \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n            \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n            \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n            \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"48 characters\">/Users/<USER>/Projects/Mega-Fly/server.php</span>\"\n            \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n            \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n            \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"124 characters\">image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,video/*;q=0.8,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5</span>\"\n            \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n            \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=5, i</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n            \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImJqcXZZRGRBTzlLVkRERjNVRUZNWGc9PSIsInZhbHVlIjoiZ1FuSmE4dExWdk83ZjJkOHdxRFBuOFVIbXE4cCtmS0NXTHVzSFlSOVR1WDRicEZKM3BLTGZvdHcxSzI1MGxWQUlpcTJVcFYyU1pEZlJlVUZhZitzU1lOVkJVYlZFVkc3TUtPZnQrNVZxY0lBMEdFSXE4N0ZKS2EzTW8vdDFrdzAiLCJtYWMiOiJiYWE0MzljNjAxNzU3OTc4NmZmNThjMGRmZDMxMTFkZTgwMGQ4OWFiMWQ1Mjk5MDI3M2YwY2Q0OGVlNTUyMTU1IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ilg2UW5LVmcvYXVOU05qMlQ1Qlhzanc9PSIsInZhbHVlIjoiQXQwWm1YbzFUWUpFbWVxL0EyOW5CaTJyeXcxdkpLSGVuSzE4VCtZN2FFODRnVFBpY0szSWswY0xEZDZqRy8zN2FLenBCQ2JHVUdJR3RpU2dYS2Z1enZOQUZBSWU2ZDloZjdhcGgvUzNOQXlkZ1VrTlJpRlk2ajBaV0R3T0lKMUUiLCJtYWMiOiI1MTRlODcwNGM2MTZiMjBmOWFlMmRkMGJkYzc2MDE3YjcyZDhlODBlNmM5OGQ3NzZmNmIzZjg5ZTRjMDE0YWMwIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n            \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n            \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812083.3342</span>\n            \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812083</span>\n          </samp>]\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#51</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#49</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImJqcXZZRGRBTzlLVkRERjNVRUZNWGc9PSIsInZhbHVlIjoiZ1FuSmE4dExWdk83ZjJkOHdxRFBuOFVIbXE4cCtmS0NXTHVzSFlSOVR1WDRicEZKM3BLTGZvdHcxSzI1MGxWQUlpcTJVcFYyU1pEZlJlVUZhZitzU1lOVkJVYlZFVkc3TUtPZnQrNVZxY0lBMEdFSXE4N0ZKS2EzTW8vdDFrdzAiLCJtYWMiOiJiYWE0MzljNjAxNzU3OTc4NmZmNThjMGRmZDMxMTFkZTgwMGQ4OWFiMWQ1Mjk5MDI3M2YwY2Q0OGVlNTUyMTU1IiwidGFnIjoiIn0=</span>\"\n            \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilg2UW5LVmcvYXVOU05qMlQ1Qlhzanc9PSIsInZhbHVlIjoiQXQwWm1YbzFUWUpFbWVxL0EyOW5CaTJyeXcxdkpLSGVuSzE4VCtZN2FFODRnVFBpY0szSWswY0xEZDZqRy8zN2FLenBCQ2JHVUdJR3RpU2dYS2Z1enZOQUZBSWU2ZDloZjdhcGgvUzNOQXlkZ1VrTlJpRlk2ajBaV0R3T0lKMUUiLCJtYWMiOiI1MTRlODcwNGM2MTZiMjBmOWFlMmRkMGJkYzc2MDE3YjcyZDhlODBlNmM5OGQ3NzZmNmIzZjg5ZTRjMDE0YWMwIiwidGFnIjoiIn0=</span>\"\n            \"<span class=sf-dump-key>dosshr_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0=</span>\"\n            \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str title=\"256 characters\">eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ==</span>\"\n            \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"256 characters\">eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ==</span>\"\n          </samp>]\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#52</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"124 characters\">image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,video/*;q=0.8,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=5, i</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImJqcXZZRGRBTzlLVkRERjNVRUZNWGc9PSIsInZhbHVlIjoiZ1FuSmE4dExWdk83ZjJkOHdxRFBuOFVIbXE4cCtmS0NXTHVzSFlSOVR1WDRicEZKM3BLTGZvdHcxSzI1MGxWQUlpcTJVcFYyU1pEZlJlVUZhZitzU1lOVkJVYlZFVkc3TUtPZnQrNVZxY0lBMEdFSXE4N0ZKS2EzTW8vdDFrdzAiLCJtYWMiOiJiYWE0MzljNjAxNzU3OTc4NmZmNThjMGRmZDMxMTFkZTgwMGQ4OWFiMWQ1Mjk5MDI3M2YwY2Q0OGVlNTUyMTU1IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ilg2UW5LVmcvYXVOU05qMlQ1Qlhzanc9PSIsInZhbHVlIjoiQXQwWm1YbzFUWUpFbWVxL0EyOW5CaTJyeXcxdkpLSGVuSzE4VCtZN2FFODRnVFBpY0szSWswY0xEZDZqRy8zN2FLenBCQ2JHVUdJR3RpU2dYS2Z1enZOQUZBSWU2ZDloZjdhcGgvUzNOQXlkZ1VrTlJpRlk2ajBaV0R3T0lKMUUiLCJtYWMiOiI1MTRlODcwNGM2MTZiMjBmOWFlMmRkMGJkYzc2MDE3YjcyZDhlODBlNmM5OGQ3NzZmNmIzZjg5ZTRjMDE0YWMwIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">image/avif</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">image/jxl</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">image/heic</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"19 characters\">image/heic-sequence</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"13 characters\">image/svg+xml</span>\"\n          <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"7 characters\">video/*</span>\"\n          <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"7 characters\">image/*</span>\"\n          <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n        #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n        #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#1458</a><samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n          <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#129</a> &#8230;}\n          <span class=sf-dump-meta>use</span>: {<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref>#2</a> &#8230;}\n          </samp>}\n          <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Auth/AuthServiceProvider.php\n106 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Auth/AuthServiceProvider.php</span>\"\n          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-const>null</span>\n        <span class=sf-dump-meta>basePath</span>: \"\"\n        <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n      </samp>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>753</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">match</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Routing\\RouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>740</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">findRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>729</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"104 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">/Users/<USER>/Projects/Mega-Fly/themes/Base/Core/Middleware/RunUpdater.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>72</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Themes\\Base\\Core\\Middleware\\RunUpdater</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2398 title=\"2 occurrences\">#398</a><samp data-depth=4 id=sf-dump-**********-ref2398 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$destination</span>: <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2407 title=\"2 occurrences\">#407</a><samp data-depth=6 id=sf-dump-**********-ref2407 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"App\\Http\\Kernel\n15 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">App\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Kernel</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"App\\Http\\Kernel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel</span> {<a class=sf-dump-ref>#33</a> &#8230;}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php\n104 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Foundation/Http/Kernel.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">187 to 191</span>\"\n          </samp>}\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">139 to 145</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"104 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2397 title=\"2 occurrences\">#397</a><samp data-depth=4 id=sf-dump-**********-ref2397 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2398 title=\"2 occurrences\">#398</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"38 characters\">Themes\\Base\\Core\\Middleware\\RunUpdater</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"126 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"134 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2186 title=\"3 occurrences\">#186</a><samp data-depth=4 id=sf-dump-**********-ref2186 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2397 title=\"2 occurrences\">#397</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2186 title=\"3 occurrences\">#186</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"126 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"120 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2185 title=\"3 occurrences\">#185</a><samp data-depth=4 id=sf-dump-**********-ref2185 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2186 title=\"3 occurrences\">#186</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2185 title=\"3 occurrences\">#185</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"125 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21411 title=\"2 occurrences\">#1411</a><samp data-depth=4 id=sf-dump-**********-ref21411 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2185 title=\"3 occurrences\">#185</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"31 characters\">App\\Http\\Middleware\\TrimStrings</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"141 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>86</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21405 title=\"2 occurrences\">#1405</a><samp data-depth=4 id=sf-dump-**********-ref21405 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21411 title=\"2 occurrences\">#1411</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"108 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21404 title=\"2 occurrences\">#1404</a><samp data-depth=4 id=sf-dump-**********-ref21404 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21405 title=\"2 occurrences\">#1405</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"52 characters\">App\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">/Users/<USER>/Projects/Mega-Fly/app/Http/Middleware/RedirectToInstaller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\RedirectToInstaller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2125 title=\"2 occurrences\">#125</a><samp data-depth=4 id=sf-dump-**********-ref2125 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21404 title=\"2 occurrences\">#1404</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"110 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>180</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref>#91</a><samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2119 title=\"9 occurrences\">#119</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2125 title=\"2 occurrences\">#125</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\RedirectToInstaller</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php\n99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/Users/<USER>/Projects/Mega-Fly/vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">/laravel/framework/</span>src/Illuminate/Pipeline/Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">156 to 187</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"99 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>116</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"104 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>165</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2407 title=\"2 occurrences\">#407</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"104 characters\">/Users/<USER>/Projects/Mega-Fly/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>134</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>71</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref246 title=\"29 occurrences\">#46</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">/Users/<USER>/Projects/Mega-Fly/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $this->getRouteForMethods($request, $others);\n", "        }\n", "\n", "        throw new NotFoundHttpException(sprintf(\n", "            'The route %s could not be found.',\n", "            $request->path()\n", "        ));\n"], "xdebug_link": null}]}, "views": {"nb_templates": 2, "templates": [{"name": "errors::404 (resources/views/errors/404.blade.php)", "param_count": 2, "params": ["errors", "exception"], "type": "blade"}, {"name": "errors.illustrated-layout (resources/views/errors/illustrated-layout.blade.php)", "param_count": 5, "params": ["__env", "app", "errors", "exception", "title"], "type": "blade"}]}, "route": [], "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gob5BwuxuGLEIytJxpHCUIEvQKqsjUqVlNb5vwyT", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg", "status_code": "<pre class=sf-dump id=sf-dump-295687291 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-295687291\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1593952282 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1593952282\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1260821794 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1260821794\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1419614252 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"124 characters\">image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,video/*;q=0.8,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=5, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImJqcXZZRGRBTzlLVkRERjNVRUZNWGc9PSIsInZhbHVlIjoiZ1FuSmE4dExWdk83ZjJkOHdxRFBuOFVIbXE4cCtmS0NXTHVzSFlSOVR1WDRicEZKM3BLTGZvdHcxSzI1MGxWQUlpcTJVcFYyU1pEZlJlVUZhZitzU1lOVkJVYlZFVkc3TUtPZnQrNVZxY0lBMEdFSXE4N0ZKS2EzTW8vdDFrdzAiLCJtYWMiOiJiYWE0MzljNjAxNzU3OTc4NmZmNThjMGRmZDMxMTFkZTgwMGQ4OWFiMWQ1Mjk5MDI3M2YwY2Q0OGVlNTUyMTU1IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ilg2UW5LVmcvYXVOU05qMlQ1Qlhzanc9PSIsInZhbHVlIjoiQXQwWm1YbzFUWUpFbWVxL0EyOW5CaTJyeXcxdkpLSGVuSzE4VCtZN2FFODRnVFBpY0szSWswY0xEZDZqRy8zN2FLenBCQ2JHVUdJR3RpU2dYS2Z1enZOQUZBSWU2ZDloZjdhcGgvUzNOQXlkZ1VrTlJpRlk2ajBaV0R3T0lKMUUiLCJtYWMiOiI1MTRlODcwNGM2MTZiMjBmOWFlMmRkMGJkYzc2MDE3YjcyZDhlODBlNmM5OGQ3NzZmNmIzZjg5ZTRjMDE0YWMwIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419614252\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-317380658 data-indent-pad=\"  \"><span class=sf-dump-note>array:26</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57975</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"48 characters\">/Users/<USER>/Projects/Mega-Fly/server.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"61 characters\">/uploads/0000/1/2025/05/18/pexels-stefanstefancik-91217-2.jpg</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"124 characters\">image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,video/*;q=0.8,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=5, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImJqcXZZRGRBTzlLVkRERjNVRUZNWGc9PSIsInZhbHVlIjoiZ1FuSmE4dExWdk83ZjJkOHdxRFBuOFVIbXE4cCtmS0NXTHVzSFlSOVR1WDRicEZKM3BLTGZvdHcxSzI1MGxWQUlpcTJVcFYyU1pEZlJlVUZhZitzU1lOVkJVYlZFVkc3TUtPZnQrNVZxY0lBMEdFSXE4N0ZKS2EzTW8vdDFrdzAiLCJtYWMiOiJiYWE0MzljNjAxNzU3OTc4NmZmNThjMGRmZDMxMTFkZTgwMGQ4OWFiMWQ1Mjk5MDI3M2YwY2Q0OGVlNTUyMTU1IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ilg2UW5LVmcvYXVOU05qMlQ1Qlhzanc9PSIsInZhbHVlIjoiQXQwWm1YbzFUWUpFbWVxL0EyOW5CaTJyeXcxdkpLSGVuSzE4VCtZN2FFODRnVFBpY0szSWswY0xEZDZqRy8zN2FLenBCQ2JHVUdJR3RpU2dYS2Z1enZOQUZBSWU2ZDloZjdhcGgvUzNOQXlkZ1VrTlJpRlk2ajBaV0R3T0lKMUUiLCJtYWMiOiI1MTRlODcwNGM2MTZiMjBmOWFlMmRkMGJkYzc2MDE3YjcyZDhlODBlNmM5OGQ3NzZmNmIzZjg5ZTRjMDE0YWMwIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812083.3342</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812083</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317380658\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1252600931 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImJqcXZZRGRBTzlLVkRERjNVRUZNWGc9PSIsInZhbHVlIjoiZ1FuSmE4dExWdk83ZjJkOHdxRFBuOFVIbXE4cCtmS0NXTHVzSFlSOVR1WDRicEZKM3BLTGZvdHcxSzI1MGxWQUlpcTJVcFYyU1pEZlJlVUZhZitzU1lOVkJVYlZFVkc3TUtPZnQrNVZxY0lBMEdFSXE4N0ZKS2EzTW8vdDFrdzAiLCJtYWMiOiJiYWE0MzljNjAxNzU3OTc4NmZmNThjMGRmZDMxMTFkZTgwMGQ4OWFiMWQ1Mjk5MDI3M2YwY2Q0OGVlNTUyMTU1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilg2UW5LVmcvYXVOU05qMlQ1Qlhzanc9PSIsInZhbHVlIjoiQXQwWm1YbzFUWUpFbWVxL0EyOW5CaTJyeXcxdkpLSGVuSzE4VCtZN2FFODRnVFBpY0szSWswY0xEZDZqRy8zN2FLenBCQ2JHVUdJR3RpU2dYS2Z1enZOQUZBSWU2ZDloZjdhcGgvUzNOQXlkZ1VrTlJpRlk2ajBaV0R3T0lKMUUiLCJtYWMiOiI1MTRlODcwNGM2MTZiMjBmOWFlMmRkMGJkYzc2MDE3YjcyZDhlODBlNmM5OGQ3NzZmNmIzZjg5ZTRjMDE0YWMwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str title=\"256 characters\">eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ==</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"256 characters\">eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ==</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252600931\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1449495651 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:28:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449495651\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-63309314 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gob5BwuxuGLEIytJxpHCUIEvQKqsjUqVlNb5vwyT</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63309314\", {\"maxDepth\":0})</script>\n"}}