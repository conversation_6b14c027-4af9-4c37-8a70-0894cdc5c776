{"__meta": {"id": "X6d8f6bf23f97b44d0eed2fe96cbdf400", "datetime": "2025-07-06 17:55:04", "utime": 1751824504.117514, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[17:55:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751824504.114601, "collector": "log"}, {"message": "[17:55:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751824504.114617, "collector": "log"}, {"message": "[17:55:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751824504.114787, "collector": "log"}, {"message": "[17:55:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751824504.114801, "collector": "log"}]}, "time": {"start": 1751824504.087398, "end": 1751824504.117523, "duration": 0.030124902725219727, "duration_str": "30.12ms", "measures": [{"label": "Booting", "start": 1751824504.087398, "relative_start": 0, "end": 1751824504.114292, "relative_end": 1751824504.114292, "duration": 0.02689385414123535, "duration_str": "26.89ms", "params": [], "collector": null}, {"label": "Application", "start": 1751824504.114436, "relative_start": 0.027037858963012695, "end": 1751824504.117524, "relative_end": 9.5367431640625e-07, "duration": 0.0030879974365234375, "duration_str": "3.09ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2731592, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-379063440 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-379063440\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1369574370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1369574370\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1756130034 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1756130034\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-752147126 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6Im03dy9hZ2laejlRMFhZMWVNS0t2QWc9PSIsInZhbHVlIjoicklxZFpXeUZ5cFRPRXF3ckE0WkxweGhGV1lZLzdLT0cyMkk2akErOUVJMnNERFZzelZxTkpqVnRobktsMm0zSkY2SG9KMm1qOGpna0lTeThXOEQ2bnlMejFGT2lNUktNY0I5ay9hNHFqME16Ri9OUWRzRkM0anhGcW5jeEJVWXEiLCJtYWMiOiJjOTk3OGEzMjg2NTRlOTY3OTIxYWEyYjA2YTZhMDc2ZWE0ZTQzNGMwMjNiMDdlMDdkNmU5MGJmODFhYjliMDg1IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6InAxWDVoc2hndGdBMHVBUlRoYVF5a1E9PSIsInZhbHVlIjoiQlpLQ2VOMnB0RlVTQmdnUnM4OWJOaDB4TWNZRjdnNSs3Q3pWTmIzczVFSGJVak5pMTdhbVVValZZM0hmWGxsMXhiVlJQV0xzNkRMd3EwK2lLOC8rOU5DK1puT28vZ3l0Sk5jbkR4UUxxVm9ZMG1Kc2REQzIyT2lKWkkrSFFXdS8iLCJtYWMiOiI5MGUxOGI5MTMzYThkMzI2ZTk4YmE2MTE3MDNmMjAwMjFlNzBkZGY1MjkyNWY2YTZkNDE1NDgxMTk3MTk2ZmZkIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-752147126\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1526430601 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61158</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6Im03dy9hZ2laejlRMFhZMWVNS0t2QWc9PSIsInZhbHVlIjoicklxZFpXeUZ5cFRPRXF3ckE0WkxweGhGV1lZLzdLT0cyMkk2akErOUVJMnNERFZzelZxTkpqVnRobktsMm0zSkY2SG9KMm1qOGpna0lTeThXOEQ2bnlMejFGT2lNUktNY0I5ay9hNHFqME16Ri9OUWRzRkM0anhGcW5jeEJVWXEiLCJtYWMiOiJjOTk3OGEzMjg2NTRlOTY3OTIxYWEyYjA2YTZhMDc2ZWE0ZTQzNGMwMjNiMDdlMDdkNmU5MGJmODFhYjliMDg1IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6InAxWDVoc2hndGdBMHVBUlRoYVF5a1E9PSIsInZhbHVlIjoiQlpLQ2VOMnB0RlVTQmdnUnM4OWJOaDB4TWNZRjdnNSs3Q3pWTmIzczVFSGJVak5pMTdhbVVValZZM0hmWGxsMXhiVlJQV0xzNkRMd3EwK2lLOC8rOU5DK1puT28vZ3l0Sk5jbkR4UUxxVm9ZMG1Kc2REQzIyT2lKWkkrSFFXdS8iLCJtYWMiOiI5MGUxOGI5MTMzYThkMzI2ZTk4YmE2MTE3MDNmMjAwMjFlNzBkZGY1MjkyNWY2YTZkNDE1NDgxMTk3MTk2ZmZkIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751824504.0874</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751824504</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526430601\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1788510525 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>royaltransit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788510525\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1319684188 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 17:55:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InhVWUliL3draGJIU3lmQ0ZhNWcyZFE9PSIsInZhbHVlIjoiNk5kWHdzZnZ5UUtxb0EzOUR1ekRWVDNkYm9ybDd4UFA4NCtLSWErc2lod3R0QUxIbDE5OFh0TlJCQ1B6YXBTMVdsS0lGZGZQdTI1LzFoWXRsaGxhK0xWVVpXNUd3Y2tGeERkQnR3ck9LeUNYNEpVTUo3bzRyOG9hVWYwc21TRVAiLCJtYWMiOiJmMjczNDcxNjc1MTJlNGJkN2YxMTE5MDg5ZmFlZWJkZTZhYjllNjg2YTM4Mjc0OTI0MzFhMmJhZDI3M2YxYjdmIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 19:55:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6InltZG9PQlJJZCtONTRwZ3BpY2k5R2c9PSIsInZhbHVlIjoiblRHTW82ZXVyNVR3aTRubDB0K24rdTVkRmdPTktESWdvRTdHQVNlY0VuaHZ1Q2hqR25odnpwU2l1K3dRd1BNRG9HQ2pmeStSalZnTjZ2anpXRWtVVEsvSm1rQzVSYTE3TmkyTEl0azhPbGM5MytGaVY4ZWtmWVpDZHZTeTdIN3MiLCJtYWMiOiI5Nzc4NDAyODdkZTc5YTM0ZThjYjViMjJkMzIyYjdmNmU4YzZiZDBiYTVkY2RkZTIxMmYwMDAzNDI3MWMzNzZiIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 19:55:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InhVWUliL3draGJIU3lmQ0ZhNWcyZFE9PSIsInZhbHVlIjoiNk5kWHdzZnZ5UUtxb0EzOUR1ekRWVDNkYm9ybDd4UFA4NCtLSWErc2lod3R0QUxIbDE5OFh0TlJCQ1B6YXBTMVdsS0lGZGZQdTI1LzFoWXRsaGxhK0xWVVpXNUd3Y2tGeERkQnR3ck9LeUNYNEpVTUo3bzRyOG9hVWYwc21TRVAiLCJtYWMiOiJmMjczNDcxNjc1MTJlNGJkN2YxMTE5MDg5ZmFlZWJkZTZhYjllNjg2YTM4Mjc0OTI0MzFhMmJhZDI3M2YxYjdmIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 19:55:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6InltZG9PQlJJZCtONTRwZ3BpY2k5R2c9PSIsInZhbHVlIjoiblRHTW82ZXVyNVR3aTRubDB0K24rdTVkRmdPTktESWdvRTdHQVNlY0VuaHZ1Q2hqR25odnpwU2l1K3dRd1BNRG9HQ2pmeStSalZnTjZ2anpXRWtVVEsvSm1rQzVSYTE3TmkyTEl0azhPbGM5MytGaVY4ZWtmWVpDZHZTeTdIN3MiLCJtYWMiOiI5Nzc4NDAyODdkZTc5YTM0ZThjYjViMjJkMzIyYjdmNmU4YzZiZDBiYTVkY2RkZTIxMmYwMDAzNDI3MWMzNzZiIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 19:55:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319684188\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1805193022 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805193022\", {\"maxDepth\":0})</script>\n"}}