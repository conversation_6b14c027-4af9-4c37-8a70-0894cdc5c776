{"__meta": {"id": "Xcb425cd15b09de15e4a84bd31a093c5b", "datetime": "2025-07-06 14:31:57", "utime": 1751812317.987933, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 16, "messages": [{"message": "[14:31:57] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.894928, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.894964, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.895137, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.895152, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_AttrTransform_NameSync::$idDef is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.975491, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_AttrTransform_NameSync::$idDef is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.975596, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.975767, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.976036, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.97777, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.977947, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.978446, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_ChildDef_List::$whitespace is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/List.php on line 34", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.978833, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.97928, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.980415, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.980769, "collector": "log"}, {"message": "[14:31:57] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812317.981047, "collector": "log"}]}, "time": {"start": 1751812317.852846, "end": 1751812317.987949, "duration": 0.1351029872894287, "duration_str": "135ms", "measures": [{"label": "Booting", "start": 1751812317.852846, "relative_start": 0, "end": 1751812317.894284, "relative_end": 1751812317.894284, "duration": 0.04143810272216797, "duration_str": "41.44ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812317.89449, "relative_start": 0.04164409637451172, "end": 1751812317.98795, "relative_end": 1.1920928955078125e-06, "duration": 0.0934600830078125, "duration_str": "93.46ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 8199480, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 43, "templates": [{"name": "Page::frontend.detail (themes/Mytravel/Page/Views/frontend/detail.blade.php)", "param_count": 4, "params": ["row", "seo_meta", "translation", "is_home"], "type": "blade"}, {"name": "Template::frontend.blocks.form-search-all-service.style_1 (themes/Mytravel/Template/Views/frontend/blocks/form-search-all-service/style_1.blade.php)", "param_count": 19, "params": ["service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.form-search (themes/Mytravel/Flight/Views/frontend/layouts/search/form-search.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "return"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.style_1 (themes/Mytravel/Location/Views/frontend/blocks/list-locations/style_1.blade.php)", "param_count": 6, "params": ["rows", "title", "desc", "service_type", "layout", "to_location_detail"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "layouts.app (resources/views/layouts/app.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home"], "type": "blade"}, {"name": "Layout::app (themes/Mytravel/Layout/app.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home"], "type": "blade"}, {"name": "Layout::parts.seo-meta (themes/Mytravel/Layout/parts/seo-meta.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.global-script (modules/Layout/parts/global-script.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.header (themes/Mytravel/Layout/parts/header.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.topbar (themes/Mytravel/Layout/parts/topbar.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Core::frontend.currency-switcher (themes/Mytravel/Core/Views/frontend/currency-switcher.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Language::frontend.switcher (themes/Mytravel/Language/Views/frontend/switcher.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.notification (themes/Mytravel/Layout/parts/notification.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Core::frontend.currency-switcher-dropdown (themes/Mytravel/Core/Views/frontend/currency-switcher-dropdown.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "logo_id", "logo"], "type": "blade"}, {"name": "Language::frontend.switcher-dropdown (themes/Mytravel/Language/Views/frontend/switcher-dropdown.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "logo_id", "logo"], "type": "blade"}, {"name": "Layout::parts.footer (themes/Mytravel/Layout/parts/footer.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Language::frontend.switcher (themes/Mytravel/Language/Views/frontend/switcher.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Core::frontend.currency-switcher (themes/Mytravel/Core/Views/frontend/currency-switcher.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::parts.login-register-modal (themes/Mytravel/Layout/parts/login-register-modal.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::auth.login-form (modules/Layout/auth/login-form.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::auth.register-form (modules/Layout/auth/register-form.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Popup::frontend.popup (themes/Mytravel/Popup/Views/frontend/popup.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Support::frontend.chat.widget (modules/Support/Views/frontend/chat/widget.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "chatConversation", "hasActiveChat", "chatMessages", "sessionId", "conversation", "messages"], "type": "blade"}, {"name": "demo_script (resources/views/demo_script.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "chatConversation", "hasActiveChat", "chatMessages", "sessionId"], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/app/Http/Controllers/HomeController.php&line=30\">app/Http/Controllers/HomeController.php:30-60</a>"}, "queries": {"nb_statements": 30, "nb_failed_statements": 0, "accumulated_duration": 0.020470000000000006, "accumulated_duration_str": "20.47ms", "statements": [{"sql": "select * from `core_pages` where `id` = '1' and `status` = 'publish' and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1", "publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 33}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "duration": 0.00429, "duration_str": "4.29ms", "stmt_id": "/app/Http/Controllers/HomeController.php:33", "connection": "megafly"}, {"sql": "select * from `core_page_translations` where `core_page_translations`.`origin_id` = 1 and `core_page_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 36}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `bravo_seo` where `object_id` = 1 and `object_model` = 'page' limit 1", "type": "query", "params": [], "bindings": ["1", "page"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/app/BaseModel.php", "line": 199}, {"index": 16, "namespace": null, "name": "/app/BaseModel.php", "line": 212}, {"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 37}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "/app/BaseModel.php:199", "connection": "megafly"}, {"sql": "select * from `core_templates` where `core_templates`.`id` = 1 and `core_templates`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 79}, {"index": 21, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.0002, "duration_str": "200μs", "stmt_id": "/modules/Page/Models/Page.php:79", "connection": "megafly"}, {"sql": "select * from `core_template_translations` where `core_template_translations`.`origin_id` = 1 and `core_template_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 81}, {"index": 22, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `name` asc limit 1000", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php", "line": 131}, {"index": 17, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 18, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 21, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php:131", "connection": "megafly"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php", "line": 131}, {"index": 22, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 23, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00017999999999999998, "duration_str": "180μs", "stmt_id": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php:131", "connection": "megafly"}, {"sql": "select * from `bravo_seat_type` where `bravo_seat_type`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php", "line": 135}, {"index": 20, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 21, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00016, "duration_str": "160μs", "stmt_id": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php:135", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `id` asc limit 6", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 159}, {"index": 15, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 126}, {"index": 18, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 19, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 21, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "/themes/Mytravel/Location/Blocks/ListLocations.php:159", "connection": "megafly"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 159}, {"index": 20, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 126}, {"index": 23, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "/themes/Mytravel/Location/Blocks/ListLocations.php:159", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 111}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = 1 and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 36}, {"index": 20, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 19}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 116}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00023999999999999998, "duration_str": "240μs", "stmt_id": "/modules/Core/Walkers/MenuWalker.php:36", "connection": "megafly"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 111}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = 1 and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 36}, {"index": 20, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 19}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 116}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00025, "duration_str": "250μs", "stmt_id": "/modules/Core/Walkers/MenuWalker.php:36", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = '' and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 627}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "/app/Helpers/AppHelper.php:627", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "2ba402c0f2d314012964e891c18a8c9c9aad769a", "line": 114}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "view::2ba402c0f2d314012964e891c18a8c9c9aad769a:114", "connection": "megafly"}]}, "models": {"data": {"Modules\\Core\\Models\\MenuTranslation": 2, "App\\Models\\Airports": 3848, "Modules\\Flight\\Models\\SeatType": 5, "Modules\\Location\\Models\\Location": 16, "Modules\\Template\\Models\\TemplateTranslation": 1, "Modules\\Template\\Models\\Template": 1, "Modules\\Core\\Models\\SEO": 1, "Modules\\Page\\Models\\PageTranslation": 1, "Modules\\Page\\Models\\Page": 3}, "count": 3878}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1515090415 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1515090415\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-91946366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-91946366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-749755746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-749755746\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1889387417 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889387417\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-544762282 data-indent-pad=\"  \"><span class=sf-dump-note>array:25</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58675</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812317.8528</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812317</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544762282\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-991727565 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-991727565\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1397869700 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:31:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBXNk5RYmVNOEhabUora0dRTHV5cnc9PSIsInZhbHVlIjoiZWE4d3ltTURxYXBhTkJGMGhaYkpZSEtRY1FxMDg4cE1ZK2dhZk9XQXlKcEpxYUdxUVQvU2ZUaVV6SFN2QWlQcEVxN09odHRHNzIwZ2MwdzBtV0p1Yy9pR2hEV3cyYW9SZDlzdGNLbThPZ3ZCUDhraHR1dDdrQlpVVnpaWjhHR3kiLCJtYWMiOiI5MWMwMmIyYWYyZTUzZTEzYTVmNTRmOTU3NjNlYWM5ZDVlNGY1MWZiMzZlMTNlMTljMjZjNWEyNGFiNTdlYTYwIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IjJ2M0VxWDFjMG9BUGE5RXg1cm5Db2c9PSIsInZhbHVlIjoiaFpRUGdNOEdMY2ltZnpTMFJnckxiTUNrN2EwMGplRXBHNmlubDQyY0RrSUVpeHFIVUVianFhaVpseCtCSUIrclhHc3VLL2ptZ1p5ZDhTM25DTzcrUjVuK3I3U1VmNVJvQ1d6eUNYbGtoaS9sd1FlRmdXaXA0VElzQTMwcERVRE0iLCJtYWMiOiI3YjNlNWQ2NmE3NzgwMjI4ZTM3NmZkOGU5NmJhYzc0MjliODJlNTZmOWMxY2NkYTg2MzBiMTJjNjEwMDQzMzk3IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBXNk5RYmVNOEhabUora0dRTHV5cnc9PSIsInZhbHVlIjoiZWE4d3ltTURxYXBhTkJGMGhaYkpZSEtRY1FxMDg4cE1ZK2dhZk9XQXlKcEpxYUdxUVQvU2ZUaVV6SFN2QWlQcEVxN09odHRHNzIwZ2MwdzBtV0p1Yy9pR2hEV3cyYW9SZDlzdGNLbThPZ3ZCUDhraHR1dDdrQlpVVnpaWjhHR3kiLCJtYWMiOiI5MWMwMmIyYWYyZTUzZTEzYTVmNTRmOTU3NjNlYWM5ZDVlNGY1MWZiMzZlMTNlMTljMjZjNWEyNGFiNTdlYTYwIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IjJ2M0VxWDFjMG9BUGE5RXg1cm5Db2c9PSIsInZhbHVlIjoiaFpRUGdNOEdMY2ltZnpTMFJnckxiTUNrN2EwMGplRXBHNmlubDQyY0RrSUVpeHFIVUVianFhaVpseCtCSUIrclhHc3VLL2ptZ1p5ZDhTM25DTzcrUjVuK3I3U1VmNVJvQ1d6eUNYbGtoaS9sd1FlRmdXaXA0VElzQTMwcERVRE0iLCJtYWMiOiI3YjNlNWQ2NmE3NzgwMjI4ZTM3NmZkOGU5NmJhYzc0MjliODJlNTZmOWMxY2NkYTg2MzBiMTJjNjEwMDQzMzk3IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397869700\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1666441028 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666441028\", {\"maxDepth\":0})</script>\n"}}