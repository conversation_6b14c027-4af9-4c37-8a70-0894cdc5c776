{"__meta": {"id": "X40a8fb957704758653f9b606b8793c0b", "datetime": "2025-07-06 14:27:08", "utime": 1751812028.215535, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:27:08] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812028.207983, "collector": "log"}, {"message": "[14:27:08] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812028.207997, "collector": "log"}, {"message": "[14:27:08] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812028.208101, "collector": "log"}, {"message": "[14:27:08] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812028.208114, "collector": "log"}]}, "time": {"start": 1751812028.180492, "end": 1751812028.215547, "duration": 0.03505516052246094, "duration_str": "35.06ms", "measures": [{"label": "Booting", "start": 1751812028.180492, "relative_start": 0, "end": 1751812028.207687, "relative_end": 1751812028.207687, "duration": 0.027194976806640625, "duration_str": "27.19ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812028.207811, "relative_start": 0.02731919288635254, "end": 1751812028.215548, "relative_end": 9.5367431640625e-07, "duration": 0.007736921310424805, "duration_str": "7.74ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3222264, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0031400000000000004, "accumulated_duration_str": "3.14ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00239, "duration_str": "2.39ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` = 'closed' and `updated_at` >= '2025-07-06 14:17:08' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "closed", "2025-07-06 14:17:08"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-687412088 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-687412088\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2004062999 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IjZlQXJSd1lVZWtxcnB1M2Y2RWxWY0E9PSIsInZhbHVlIjoiNndiTFNJWDFIbU9VUEhuOVBYbTU3T2VkM25rSUhPWDlwSkNJY0tlN0FkVmFjQ1ZkOTJrVDZXV3ZhN1ZGaHkvWVZ1dm11eXVoT0NjNlhCOHpjdHZqa1FNenVoVTE5UVA2bkorK0lZMXNFNDYxYlg3ODNmMzEydUw4bDQrTlZZSXoiLCJtYWMiOiI2YTEyM2VmYjIyZmZlZTk1MzU4NjM2NzQ0ZTFmMGY1YTYxNGNmZDE4MjIxMGRlYTI3M2FiODZhNDlmOGRjZDc4IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Inl4dWFrSXBadUpxNW5NVEd4WDRaeGc9PSIsInZhbHVlIjoiNk9OT3V5NWQ2dVBQV3NlUkxvRURvN04vWE9JY2lzVnRJY1h1SmZ0RmMrWlpWU3ppOWl0ck5qb3lrdlI0bFZwMnhvV2Fvc0gvUXRUZnZFelptcXkwdTBXdS85WG4wRTc0ZmNnWlQ4TURwbGdhS1dGVTJWQ25nV0FtRnBBM05SYnMiLCJtYWMiOiJiNTM3ZTljYzE5YTFlNjdhNmI1ZWNjYmFjNjQxNDc4Y2NjNWY5MGNjZWNlMjlmMzVlOTdjY2I5OTJlYjk3MmJiIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004062999\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1445795978 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57649</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IjZlQXJSd1lVZWtxcnB1M2Y2RWxWY0E9PSIsInZhbHVlIjoiNndiTFNJWDFIbU9VUEhuOVBYbTU3T2VkM25rSUhPWDlwSkNJY0tlN0FkVmFjQ1ZkOTJrVDZXV3ZhN1ZGaHkvWVZ1dm11eXVoT0NjNlhCOHpjdHZqa1FNenVoVTE5UVA2bkorK0lZMXNFNDYxYlg3ODNmMzEydUw4bDQrTlZZSXoiLCJtYWMiOiI2YTEyM2VmYjIyZmZlZTk1MzU4NjM2NzQ0ZTFmMGY1YTYxNGNmZDE4MjIxMGRlYTI3M2FiODZhNDlmOGRjZDc4IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Inl4dWFrSXBadUpxNW5NVEd4WDRaeGc9PSIsInZhbHVlIjoiNk9OT3V5NWQ2dVBQV3NlUkxvRURvN04vWE9JY2lzVnRJY1h1SmZ0RmMrWlpWU3ppOWl0ck5qb3lrdlI0bFZwMnhvV2Fvc0gvUXRUZnZFelptcXkwdTBXdS85WG4wRTc0ZmNnWlQ4TURwbGdhS1dGVTJWQ25nV0FtRnBBM05SYnMiLCJtYWMiOiJiNTM3ZTljYzE5YTFlNjdhNmI1ZWNjYmFjNjQxNDc4Y2NjNWY5MGNjZWNlMjlmMzVlOTdjY2I5OTJlYjk3MmJiIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812028.1805</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812028</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445795978\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-189029584 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-189029584\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-10358186 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:27:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InQ0UFZWQWhjZmRhRHJkQmlYT3pvL2c9PSIsInZhbHVlIjoiSHF6MTdZZkc2ZlQya2QzM29XTEYyLzBodytXKzhoQlRERENOeXlBWkhMWjNlOG1Bdi85eUFsYkFKNC9QYzVvM3lscUx1dE4xR3NKQnZIRWJhQjJhU0NnaUduUzdMSi9BczRBS2J0NnptUVBINnMxU1M3N1JyQ0ZMVWRxQTd0YWQiLCJtYWMiOiJmNjA2MTdkYzI2NjMzNWUzNzBjOTBkMDBmNDFiNTZjZDE2NzZmOTNlOWQ5NTkzMmE3OTJkYjQzYmEzZTdiNjk4IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:27:08 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IkQ0SCtUOHRJWjB3K3JzSVR5MGhzR3c9PSIsInZhbHVlIjoiN1Jva2luTTkzdzNEb21Pb3hPT21aazFqaEkwNzZRYTRwZE4yMXl1OVFNZTNhMnRkNkhDditnSkNQRHE3Mnc2TTBUSFdxVkFic3RaNDJhWU9qbjdKeUp0M2ZhWXR6a3hTTmYwc1VJOFpGYlRFWDI4UDljZjNIZENyQnBBU21JOFUiLCJtYWMiOiIzMWNkZThhZWFhNmUxYmU1MzVmM2Y2YzJmNmQ3MTAzNjY5YzM1NTlhNDc5YTA2N2ZkYmI0YTIxZTgxMmNiODBmIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:27:08 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InQ0UFZWQWhjZmRhRHJkQmlYT3pvL2c9PSIsInZhbHVlIjoiSHF6MTdZZkc2ZlQya2QzM29XTEYyLzBodytXKzhoQlRERENOeXlBWkhMWjNlOG1Bdi85eUFsYkFKNC9QYzVvM3lscUx1dE4xR3NKQnZIRWJhQjJhU0NnaUduUzdMSi9BczRBS2J0NnptUVBINnMxU1M3N1JyQ0ZMVWRxQTd0YWQiLCJtYWMiOiJmNjA2MTdkYzI2NjMzNWUzNzBjOTBkMDBmNDFiNTZjZDE2NzZmOTNlOWQ5NTkzMmE3OTJkYjQzYmEzZTdiNjk4IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:27:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IkQ0SCtUOHRJWjB3K3JzSVR5MGhzR3c9PSIsInZhbHVlIjoiN1Jva2luTTkzdzNEb21Pb3hPT21aazFqaEkwNzZRYTRwZE4yMXl1OVFNZTNhMnRkNkhDditnSkNQRHE3Mnc2TTBUSFdxVkFic3RaNDJhWU9qbjdKeUp0M2ZhWXR6a3hTTmYwc1VJOFpGYlRFWDI4UDljZjNIZENyQnBBU21JOFUiLCJtYWMiOiIzMWNkZThhZWFhNmUxYmU1MzVmM2Y2YzJmNmQ3MTAzNjY5YzM1NTlhNDc5YTA2N2ZkYmI0YTIxZTgxMmNiODBmIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:27:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10358186\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1681728764 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681728764\", {\"maxDepth\":0})</script>\n"}}