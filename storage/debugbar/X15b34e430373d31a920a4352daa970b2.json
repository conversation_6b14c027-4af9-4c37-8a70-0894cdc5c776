{"__meta": {"id": "X15b34e430373d31a920a4352daa970b2", "datetime": "2025-07-06 18:12:20", "utime": 1751825540.041755, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[18:12:20] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825540.034843, "collector": "log"}, {"message": "[18:12:20] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825540.034859, "collector": "log"}, {"message": "[18:12:20] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825540.034995, "collector": "log"}, {"message": "[18:12:20] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825540.03501, "collector": "log"}]}, "time": {"start": 1751825540.004897, "end": 1751825540.041763, "duration": 0.036865949630737305, "duration_str": "36.87ms", "measures": [{"label": "Booting", "start": 1751825540.004897, "relative_start": 0, "end": 1751825540.034455, "relative_end": 1751825540.034455, "duration": 0.02955794334411621, "duration_str": "29.56ms", "params": [], "collector": null}, {"label": "Application", "start": 1751825540.034603, "relative_start": 0.02970600128173828, "end": 1751825540.041764, "relative_end": 9.5367431640625e-07, "duration": 0.00716090202331543, "duration_str": "7.16ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3222552, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0025700000000000002, "accumulated_duration_str": "2.57ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00199, "duration_str": "1.99ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` = 'closed' and `updated_at` >= '2025-07-06 18:02:20' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "closed", "2025-07-06 18:02:20"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-305355898 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-305355898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1354493207 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"163 characters\">http://127.0.0.1:8001/flight?from_where=CAI&amp;to_where=JED&amp;start_oneway=06%2F07%2F2025&amp;start_roundtrip=07%2F07%2F2025&amp;adults=1&amp;children=0&amp;infants=0&amp;seat_type=economy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6InIxc2hlVktESXlxUHEvMVNvTnlNSGc9PSIsInZhbHVlIjoiNVc4WjBOR2VTT2t2dG1NenI5THllQllYNlQ5aHhLR3pmUjRDREZQZmwxRlo0TmYwS1FZRVRCMDQ0QnJkTWRkNVZnWEJtbGRKaXE5RWNTWGpyNHVTNTVXUTEvV2V4Z2h5VjFpYitGQnNNaHlhWEtrMTNTMER3TDJXY0xxQTlnWUQiLCJtYWMiOiJkNmRhNTViOWI2MjkxMDIwODg4OTRlMWUwMWVmMmU2YWZiMWVmMmI1Y2UyYTYwYjAxYTM2MTM5MzVjMjA3ZTI5IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlNsRmdmK2ZUWjJkZHRvYnkzV1VhN3c9PSIsInZhbHVlIjoiY2tEOGpab1ArYUhsT015bVF3aGQ3WDNlbUdUeVprcXRsNFhYQWZjc1hyWWtFa3BqZnZVWjlrR2NzdlNwTXFBR2UyaU0wcUlpSkxEaWN5QjBPT2tteHBzUHpvNnVrSzdZZWpja0E0N1lRRGpBOEQwUG9DWHltTmR3V3dYRDhxaUQiLCJtYWMiOiIyNmY3OGEwN2NjYmZjOGYyZjFhYmU4NjEzZDU1NGE3MWQ3MmFjNDU4ZTg5OTZhMTU1YzcyNjUxZjYyNWQzYWVkIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354493207\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-547688146 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">51010</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"163 characters\">http://127.0.0.1:8001/flight?from_where=CAI&amp;to_where=JED&amp;start_oneway=06%2F07%2F2025&amp;start_roundtrip=07%2F07%2F2025&amp;adults=1&amp;children=0&amp;infants=0&amp;seat_type=economy</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6InIxc2hlVktESXlxUHEvMVNvTnlNSGc9PSIsInZhbHVlIjoiNVc4WjBOR2VTT2t2dG1NenI5THllQllYNlQ5aHhLR3pmUjRDREZQZmwxRlo0TmYwS1FZRVRCMDQ0QnJkTWRkNVZnWEJtbGRKaXE5RWNTWGpyNHVTNTVXUTEvV2V4Z2h5VjFpYitGQnNNaHlhWEtrMTNTMER3TDJXY0xxQTlnWUQiLCJtYWMiOiJkNmRhNTViOWI2MjkxMDIwODg4OTRlMWUwMWVmMmU2YWZiMWVmMmI1Y2UyYTYwYjAxYTM2MTM5MzVjMjA3ZTI5IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlNsRmdmK2ZUWjJkZHRvYnkzV1VhN3c9PSIsInZhbHVlIjoiY2tEOGpab1ArYUhsT015bVF3aGQ3WDNlbUdUeVprcXRsNFhYQWZjc1hyWWtFa3BqZnZVWjlrR2NzdlNwTXFBR2UyaU0wcUlpSkxEaWN5QjBPT2tteHBzUHpvNnVrSzdZZWpja0E0N1lRRGpBOEQwUG9DWHltTmR3V3dYRDhxaUQiLCJtYWMiOiIyNmY3OGEwN2NjYmZjOGYyZjFhYmU4NjEzZDU1NGE3MWQ3MmFjNDU4ZTg5OTZhMTU1YzcyNjUxZjYyNWQzYWVkIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751825540.0049</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751825540</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-547688146\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-714582933 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714582933\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1575662777 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 18:12:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjhrN014bHZpcUszTEdvcGMvcTNlbFE9PSIsInZhbHVlIjoiOWhYNnU4M2xtSjMrSjRGWXlIUXFHYmlNRVgyUlJHTGtoTnllMFJwRzQ4QlYvYyt0YUdEbkk4OG9yNjU2eGZxc1FnZTYwZVVZYzE0c0FLWFkwSjJOaExhZkxnRGRubkhLMFBkL3o2NFhFcDFpQ09JU2tvNWxqQ0lBbllGUHVRSFUiLCJtYWMiOiI0ZTVhMzUwYjAyMzgzYTBmZTQwNjc2NTlhYzNhYzk1NzI1OWM3YTdiZmQ3Y2ZlYTZiOGYxMTUyNzlmYjA4MWUwIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:12:20 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IjZVaU1FTUNSdnpoMXFOb2k2LzR2RlE9PSIsInZhbHVlIjoieXNrM3ZGakNzR0FVcUQ2UkYxcDVHV1NTY2JMaXRZZE9YUzlRV3IxeDhqZXVveG4zVVlUSkFXS21PMFIvZjNGVzBKVFlIMFZsWmJwV3VrZ3RSVm4rOHNjOWo1Rk1HblFUclVKaG5UblZtTHhUeEdHSE5kN1RKaWlSeEh4Yy9qRUciLCJtYWMiOiI3MTFhYjhkMGViN2Y3MDVlODlhOGM3YTE0ZGMzMDhkNTBkYjFiMzkyOWMxN2E1YTBmN2U5MGQ1Zjk2NjUxNzQ0IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:12:20 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjhrN014bHZpcUszTEdvcGMvcTNlbFE9PSIsInZhbHVlIjoiOWhYNnU4M2xtSjMrSjRGWXlIUXFHYmlNRVgyUlJHTGtoTnllMFJwRzQ4QlYvYyt0YUdEbkk4OG9yNjU2eGZxc1FnZTYwZVVZYzE0c0FLWFkwSjJOaExhZkxnRGRubkhLMFBkL3o2NFhFcDFpQ09JU2tvNWxqQ0lBbllGUHVRSFUiLCJtYWMiOiI0ZTVhMzUwYjAyMzgzYTBmZTQwNjc2NTlhYzNhYzk1NzI1OWM3YTdiZmQ3Y2ZlYTZiOGYxMTUyNzlmYjA4MWUwIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:12:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IjZVaU1FTUNSdnpoMXFOb2k2LzR2RlE9PSIsInZhbHVlIjoieXNrM3ZGakNzR0FVcUQ2UkYxcDVHV1NTY2JMaXRZZE9YUzlRV3IxeDhqZXVveG4zVVlUSkFXS21PMFIvZjNGVzBKVFlIMFZsWmJwV3VrZ3RSVm4rOHNjOWo1Rk1HblFUclVKaG5UblZtTHhUeEdHSE5kN1RKaWlSeEh4Yy9qRUciLCJtYWMiOiI3MTFhYjhkMGViN2Y3MDVlODlhOGM3YTE0ZGMzMDhkNTBkYjFiMzkyOWMxN2E1YTBmN2U5MGQ1Zjk2NjUxNzQ0IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:12:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575662777\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-73062750 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73062750\", {\"maxDepth\":0})</script>\n"}}