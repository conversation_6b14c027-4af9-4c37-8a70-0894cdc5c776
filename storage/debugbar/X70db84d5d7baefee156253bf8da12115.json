{"__meta": {"id": "X70db84d5d7baefee156253bf8da12115", "datetime": "2025-07-06 17:46:47", "utime": 1751824007.989556, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[17:46:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751824007.987798, "collector": "log"}, {"message": "[17:46:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751824007.987812, "collector": "log"}, {"message": "[17:46:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751824007.98792, "collector": "log"}, {"message": "[17:46:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751824007.987934, "collector": "log"}]}, "time": {"start": 1751824007.965058, "end": 1751824007.989564, "duration": 0.0245058536529541, "duration_str": "24.51ms", "measures": [{"label": "Booting", "start": 1751824007.965058, "relative_start": 0, "end": 1751824007.9875, "relative_end": 1751824007.9875, "duration": 0.022441864013671875, "duration_str": "22.44ms", "params": [], "collector": null}, {"label": "Application", "start": 1751824007.987624, "relative_start": 0.022565841674804688, "end": 1751824007.989565, "relative_end": 9.5367431640625e-07, "duration": 0.0019409656524658203, "duration_str": "1.94ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2731592, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-2065196077 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2065196077\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-84850582 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-84850582\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1383509875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1383509875\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-868062140 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6IitoWGNZSEsrR1Q0MEJjM200ZExONHc9PSIsInZhbHVlIjoiS1owWFNlT0VJWG00OUtiZWx4NDJwMmw3KzRRVVBoUmx6NTRBN0NLSmVqTDFteS9vaDlpQzBYTHBBMUtQa2o0VW9IcTBwV2pIUlJ2YVE5TkpiZVNSUE9NcVV2UWhJV3N1WE8wODhPYW94T3BMQlhmaGZ1dFRXVUZ5YWYyZDFLU3YiLCJtYWMiOiJiY2UwNzRkZmMwMThjMDhiMGNmZTZjYWQxMGUzZTE0NjM2NzEyMjEzYTc0ZDdmY2E4MTJkNTc2YzcxZmViMTUyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6ImI0dmFlalc0a09BczYwUitUMHZheUE9PSIsInZhbHVlIjoidmlmVXlTZFZPZXpwbEpUMnRMYVoyY0NaNTA1eWVCMVJCZFBKd21jTHhURnVVQ01oS29IZHpPeXFWWi9aNUJYK092aDZMMkJLSmdobVUrOFVBRnF1UlJzRjBCQkp6WGd4K2xjd2htMDBQa2NYVWVMaHVvTWRnN3dWdmhUUE1JWEQiLCJtYWMiOiJhYTRkNjhlYzQ3YzY5Y2RmNWE3NjE4YTIyNDVlYmZhZjBhYmE2ZjNmOGVjODE4YTJmZmFmMjVkYmQyZWQ0OTM4IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868062140\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1778411333 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57331</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6IitoWGNZSEsrR1Q0MEJjM200ZExONHc9PSIsInZhbHVlIjoiS1owWFNlT0VJWG00OUtiZWx4NDJwMmw3KzRRVVBoUmx6NTRBN0NLSmVqTDFteS9vaDlpQzBYTHBBMUtQa2o0VW9IcTBwV2pIUlJ2YVE5TkpiZVNSUE9NcVV2UWhJV3N1WE8wODhPYW94T3BMQlhmaGZ1dFRXVUZ5YWYyZDFLU3YiLCJtYWMiOiJiY2UwNzRkZmMwMThjMDhiMGNmZTZjYWQxMGUzZTE0NjM2NzEyMjEzYTc0ZDdmY2E4MTJkNTc2YzcxZmViMTUyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6ImI0dmFlalc0a09BczYwUitUMHZheUE9PSIsInZhbHVlIjoidmlmVXlTZFZPZXpwbEpUMnRMYVoyY0NaNTA1eWVCMVJCZFBKd21jTHhURnVVQ01oS29IZHpPeXFWWi9aNUJYK092aDZMMkJLSmdobVUrOFVBRnF1UlJzRjBCQkp6WGd4K2xjd2htMDBQa2NYVWVMaHVvTWRnN3dWdmhUUE1JWEQiLCJtYWMiOiJhYTRkNjhlYzQ3YzY5Y2RmNWE3NjE4YTIyNDVlYmZhZjBhYmE2ZjNmOGVjODE4YTJmZmFmMjVkYmQyZWQ0OTM4IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751824007.9651</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751824007</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778411333\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2003698055 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>royaltransit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003698055\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1322097815 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 17:46:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlRrQnZ2MDhaQ3h5WkdzcmhCRCtkYXc9PSIsInZhbHVlIjoiZ25ySDdkWmdYQjR3UEFQMXhIRm14cGIrbVVORjQxWk12QTJqWmprWnlYZ2twTzdmcCtHSjZDOXNRSHIxLzg3T3d2cGc5eDlVZmh5UUlwK0t5SklwUEZid2txZWhCV3pVd2NVTG51VGw2RHByL3RQdXErVHhpY040bklCaldwV1oiLCJtYWMiOiIyNmNkMmE1Mjg4OTQ1NTEyMmYxODBlMWM2YzA4ZDAzNDA2ZmZkMDM1YzE3ODUxMTNlM2ZhMTBhZGM3ZWEwMzYxIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 19:46:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6InUzTkNYNFV6cE5QK1FRODNzSFNiaUE9PSIsInZhbHVlIjoibk51WFFUK0F2L08rZmxacEpnVlRxUnNnM1J6M1l5dVltTHRQYlFlSXZscHlia1kxLzhDQkpyVlVjcm1NeUYyR1NLNWJncU14bmphUkpGNmxYeUNkdkJ5Qmx4NkNwa29MTVZwZTNvMEpvVXo3WlhlRGZTMzR4TTE1Ylh6c3AzZ2MiLCJtYWMiOiI5ZDU1ZjI4NWVjZmNkYzllYmY5MzFhZmZlMjY0MGJmOWMzY2MzNDI0Mzc4ZDgwZjcyYzZiOTYyMDYyMTRkOWM2IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 19:46:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlRrQnZ2MDhaQ3h5WkdzcmhCRCtkYXc9PSIsInZhbHVlIjoiZ25ySDdkWmdYQjR3UEFQMXhIRm14cGIrbVVORjQxWk12QTJqWmprWnlYZ2twTzdmcCtHSjZDOXNRSHIxLzg3T3d2cGc5eDlVZmh5UUlwK0t5SklwUEZid2txZWhCV3pVd2NVTG51VGw2RHByL3RQdXErVHhpY040bklCaldwV1oiLCJtYWMiOiIyNmNkMmE1Mjg4OTQ1NTEyMmYxODBlMWM2YzA4ZDAzNDA2ZmZkMDM1YzE3ODUxMTNlM2ZhMTBhZGM3ZWEwMzYxIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 19:46:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6InUzTkNYNFV6cE5QK1FRODNzSFNiaUE9PSIsInZhbHVlIjoibk51WFFUK0F2L08rZmxacEpnVlRxUnNnM1J6M1l5dVltTHRQYlFlSXZscHlia1kxLzhDQkpyVlVjcm1NeUYyR1NLNWJncU14bmphUkpGNmxYeUNkdkJ5Qmx4NkNwa29MTVZwZTNvMEpvVXo3WlhlRGZTMzR4TTE1Ylh6c3AzZ2MiLCJtYWMiOiI5ZDU1ZjI4NWVjZmNkYzllYmY5MzFhZmZlMjY0MGJmOWMzY2MzNDI0Mzc4ZDgwZjcyYzZiOTYyMDYyMTRkOWM2IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 19:46:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322097815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-162810518 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162810518\", {\"maxDepth\":0})</script>\n"}}