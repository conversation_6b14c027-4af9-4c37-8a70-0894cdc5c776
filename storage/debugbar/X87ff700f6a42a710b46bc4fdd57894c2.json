{"__meta": {"id": "X87ff700f6a42a710b46bc4fdd57894c2", "datetime": "2025-07-06 14:33:22", "utime": 1751812402.632163, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:33:22] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812402.624472, "collector": "log"}, {"message": "[14:33:22] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812402.624486, "collector": "log"}, {"message": "[14:33:22] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812402.624585, "collector": "log"}, {"message": "[14:33:22] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812402.624599, "collector": "log"}]}, "time": {"start": 1751812402.589572, "end": 1751812402.632173, "duration": 0.04260110855102539, "duration_str": "42.6ms", "measures": [{"label": "Booting", "start": 1751812402.589572, "relative_start": 0, "end": 1751812402.624114, "relative_end": 1751812402.624114, "duration": 0.034542083740234375, "duration_str": "34.54ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812402.624284, "relative_start": 0.03471207618713379, "end": 1751812402.632174, "relative_end": 9.5367431640625e-07, "duration": 0.007889986038208008, "duration_str": "7.89ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3217912, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00402, "accumulated_duration_str": "4.02ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.0029500000000000004, "duration_str": "2.95ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` = 'closed' and `updated_at` >= '2025-07-06 14:23:22' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "closed", "2025-07-06 14:23:22"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1259617904 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1259617904\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IkNvcm55cGRIVFVvK2thMEYxdXdLbUE9PSIsInZhbHVlIjoiYmk2RGpNeUFyTXJxK3pIRlpjY245YWhOWG43WUg1MmEyL0IzS1NNTi9NcDRpMnBQa3laeGNiS1libkdvM0pWWnM0aisvY2pKenZJS3VETkdWdksvV0RlQ1dxbkdNSE1MRzBvc3RqUjVRTFBtcUhFUW9uZXB4djhGL0p4Qk1xNTMiLCJtYWMiOiI3MDZiYzk4MDE0MjY3Mjg4OGI4Y2FlNTczYjc3ZWQ4NzQ0NGM1N2Q4M2Q5ODE5YjRiNDYzNDgwYjgzMzIyZTRhIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlNNNURzWWlvMC80ZzVBWTJIV2ZHcUE9PSIsInZhbHVlIjoiNVUxa1d0VlZsWk1oY0UwZUpJaWZmYWtIbUMxNExmcWhVcld3NVAyUHVRUHd4VklGWlRIV1AxUFJmWENLZi9RTFNmM1dBYmtMOGtqOUQvK3l5dmE5VjJSY0xTOW1RRjAwUng0b295ZlluMmx0T3hTMnVHWTczM01oOXRWeXVzUXIiLCJtYWMiOiJhOWMxYmI0Zjc4OTMzMjRjNjFjODE3ZTYxYWUwNjQyMzY0MGRmZTkzNTlmZTU1MDliMzRkNTU0ZDQzYjYwZjVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58958</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IkNvcm55cGRIVFVvK2thMEYxdXdLbUE9PSIsInZhbHVlIjoiYmk2RGpNeUFyTXJxK3pIRlpjY245YWhOWG43WUg1MmEyL0IzS1NNTi9NcDRpMnBQa3laeGNiS1libkdvM0pWWnM0aisvY2pKenZJS3VETkdWdksvV0RlQ1dxbkdNSE1MRzBvc3RqUjVRTFBtcUhFUW9uZXB4djhGL0p4Qk1xNTMiLCJtYWMiOiI3MDZiYzk4MDE0MjY3Mjg4OGI4Y2FlNTczYjc3ZWQ4NzQ0NGM1N2Q4M2Q5ODE5YjRiNDYzNDgwYjgzMzIyZTRhIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlNNNURzWWlvMC80ZzVBWTJIV2ZHcUE9PSIsInZhbHVlIjoiNVUxa1d0VlZsWk1oY0UwZUpJaWZmYWtIbUMxNExmcWhVcld3NVAyUHVRUHd4VklGWlRIV1AxUFJmWENLZi9RTFNmM1dBYmtMOGtqOUQvK3l5dmE5VjJSY0xTOW1RRjAwUng0b295ZlluMmx0T3hTMnVHWTczM01oOXRWeXVzUXIiLCJtYWMiOiJhOWMxYmI0Zjc4OTMzMjRjNjFjODE3ZTYxYWUwNjQyMzY0MGRmZTkzNTlmZTU1MDliMzRkNTU0ZDQzYjYwZjVjIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812402.5896</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812402</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1937356098 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937356098\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-614010690 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:33:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdKanBvSmpsalIrYVpETGZxUU9YNkE9PSIsInZhbHVlIjoiNlVJd2RpdlI2R1U5ZklQQktRNk16RDh6alczcTJMc0F3SHFDMjZCOHVoc1hjcHZoS1dRV3Y2aW84cFpxTEVsSGhMa09HUTErZ01HZk9TbWU0dUhNYnJQNXhJR0FWTWpwRmhwTGJTTlhEeHFtekpoUmNPZE5QYzhQMldLL1ErTjIiLCJtYWMiOiI3N2VmYmY5N2RjYmU3NGI3YTAyZWU5YWMyNzcxMDRhNDcyOTRiNGE5ZTE0YmVkODFlOGZhNTkxYmUzMjE1MmQ2IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:22 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6ImNtRkVCNTR3M2g5aXRSSnQ5RjM2TUE9PSIsInZhbHVlIjoia2o1L1pXVERpNklWd1M2OVRWY2lvZmR0QndVbFVaeHBBVnhHWm1DWUtzSW42ODlNMVFwamN5amF6eEVyb29BMDNqUEUyek5Qc292TWNOTkwvZlhxOU43Y0lqQmg2TVB0SmVKTzNqWExCT1Vyb1FqZDFmZkZTMVRZbmF2amZwbEgiLCJtYWMiOiIxMTU0NDlhZDQzYTE5YjYwOTVmODk5Y2E4ODMwYjEzMzJlOGU1MjNiMzYyM2MzN2M0YTMwNzYzMmRmNmM5MzQ5IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:22 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdKanBvSmpsalIrYVpETGZxUU9YNkE9PSIsInZhbHVlIjoiNlVJd2RpdlI2R1U5ZklQQktRNk16RDh6alczcTJMc0F3SHFDMjZCOHVoc1hjcHZoS1dRV3Y2aW84cFpxTEVsSGhMa09HUTErZ01HZk9TbWU0dUhNYnJQNXhJR0FWTWpwRmhwTGJTTlhEeHFtekpoUmNPZE5QYzhQMldLL1ErTjIiLCJtYWMiOiI3N2VmYmY5N2RjYmU3NGI3YTAyZWU5YWMyNzcxMDRhNDcyOTRiNGE5ZTE0YmVkODFlOGZhNTkxYmUzMjE1MmQ2IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6ImNtRkVCNTR3M2g5aXRSSnQ5RjM2TUE9PSIsInZhbHVlIjoia2o1L1pXVERpNklWd1M2OVRWY2lvZmR0QndVbFVaeHBBVnhHWm1DWUtzSW42ODlNMVFwamN5amF6eEVyb29BMDNqUEUyek5Qc292TWNOTkwvZlhxOU43Y0lqQmg2TVB0SmVKTzNqWExCT1Vyb1FqZDFmZkZTMVRZbmF2amZwbEgiLCJtYWMiOiIxMTU0NDlhZDQzYTE5YjYwOTVmODk5Y2E4ODMwYjEzMzJlOGU1MjNiMzYyM2MzN2M0YTMwNzYzMmRmNmM5MzQ5IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-614010690\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-791964703 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791964703\", {\"maxDepth\":0})</script>\n"}}