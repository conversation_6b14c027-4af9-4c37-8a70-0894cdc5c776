{"__meta": {"id": "X9d4b58ff9935b13bc00677e5af66d733", "datetime": "2025-07-06 18:10:43", "utime": 1751825443.149539, "method": "GET", "uri": "/flight?from_where=CAI&to_where=JED&start_oneway=06%2F07%2F2025&start_roundtrip=07%2F07%2F2025&adults=1&children=0&infants=0&seat_type=economy", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 16, "messages": [{"message": "[18:10:39] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825439.92155, "collector": "log"}, {"message": "[18:10:39] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825439.921564, "collector": "log"}, {"message": "[18:10:39] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825439.921654, "collector": "log"}, {"message": "[18:10:39] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825439.921667, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_AttrTransform_NameSync::$idDef is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.096942, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_AttrTransform_NameSync::$idDef is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.097213, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.097711, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.098609, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.103828, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.104101, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.10484, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_ChildDef_List::$whitespace is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/List.php on line 34", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.105289, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.105944, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.10706, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.107784, "collector": "log"}, {"message": "[18:10:43] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751825443.108426, "collector": "log"}]}, "time": {"start": 1751825439.898846, "end": 1751825443.149582, "duration": 3.2507359981536865, "duration_str": "3.25s", "measures": [{"label": "Booting", "start": 1751825439.898846, "relative_start": 0, "end": 1751825439.921277, "relative_end": 1751825439.921277, "duration": 0.022431135177612305, "duration_str": "22.43ms", "params": [], "collector": null}, {"label": "Application", "start": 1751825439.921385, "relative_start": 0.022539138793945312, "end": 1751825443.149585, "relative_end": 3.0994415283203125e-06, "duration": 3.2281999588012695, "duration_str": "3.23s", "params": [], "collector": null}]}, "memory": {"peak_usage": 8415704, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 45, "templates": [{"name": "Flight::frontend.search (themes/Mytravel/Flight/Views/frontend/search.blade.php)", "param_count": 9, "params": ["rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.form-search (themes/Mytravel/Flight/Views/frontend/layouts/search/form-search.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.list-item (themes/Mytravel/Flight/Views/frontend/layouts/search/list-item.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.loop-grid-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/loop-grid-flight.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.modal-form-book (themes/Mytravel/Flight/Views/frontend/layouts/search/modal-form-book.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "__currentLoopData", "row", "index", "loop", "segments", "airlines", "airlineDetails", "totalPrice", "journey", "seg", "carrierCode", "carrierName", "airlineKey", "isOneWay", "hasTransit", "isDirect", "firstDeparture"], "type": "blade"}, {"name": "layouts.app (resources/views/layouts/app.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Layout::app (themes/Mytravel/Layout/app.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes"], "type": "blade"}, {"name": "Layout::parts.seo-meta (themes/Mytravel/Layout/parts/seo-meta.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.global-script (modules/Layout/parts/global-script.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.header (themes/Mytravel/Layout/parts/header.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.topbar (themes/Mytravel/Layout/parts/topbar.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Core::frontend.currency-switcher (themes/Mytravel/Core/Views/frontend/currency-switcher.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Language::frontend.switcher (themes/Mytravel/Language/Views/frontend/switcher.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.notification (themes/Mytravel/Layout/parts/notification.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Core::frontend.currency-switcher-dropdown (themes/Mytravel/Core/Views/frontend/currency-switcher-dropdown.blade.php)", "param_count": 16, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "logo_id", "logo"], "type": "blade"}, {"name": "Language::frontend.switcher-dropdown (themes/Mytravel/Language/Views/frontend/switcher-dropdown.blade.php)", "param_count": 16, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "logo_id", "logo"], "type": "blade"}, {"name": "Layout::parts.footer (themes/Mytravel/Layout/parts/footer.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file"], "type": "blade"}, {"name": "Language::frontend.switcher (themes/Mytravel/Language/Views/frontend/switcher.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Core::frontend.currency-switcher (themes/Mytravel/Core/Views/frontend/currency-switcher.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::parts.login-register-modal (themes/Mytravel/Layout/parts/login-register-modal.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::auth.login-form (modules/Layout/auth/login-form.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::auth.register-form (modules/Layout/auth/register-form.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Popup::frontend.popup (themes/Mytravel/Popup/Views/frontend/popup.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Support::frontend.chat.widget (modules/Support/Views/frontend/chat/widget.blade.php)", "param_count": 20, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "chatConversation", "hasActiveChat", "chatMessages", "sessionId", "conversation", "messages"], "type": "blade"}, {"name": "demo_script (resources/views/demo_script.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "rows", "list_location", "seatType", "flight_min_max_price", "markers", "blank", "seo_meta", "flights", "attributes", "favicon", "file", "chatConversation", "hasActiveChat", "chatMessages", "sessionId"], "type": "blade"}]}, "route": {"uri": "GET flight", "middleware": "web", "controller": "Modules\\Flight\\Controllers\\FlightController@index", "namespace": "Modules\\Flight\\Controllers", "prefix": "/flight", "where": [], "as": "flight.search", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Flight/Controllers/FlightController.php&line=43\">modules/Flight/Controllers/FlightController.php:43-104</a>"}, "queries": {"nb_statements": 24, "nb_failed_statements": 0, "accumulated_duration": 0.03938, "accumulated_duration_str": "39.38ms", "statements": [{"sql": "select count(*) as aggregate from `bravo_flight` where `status` = 'publish' and exists (select * from `bravo_flight_seat` where `bravo_flight`.`id` = `bravo_flight_seat`.`flight_id` and `bravo_flight_seat`.`deleted_at` is null) and exists (select * from `bravo_airport` where `bravo_flight`.`airport_from` = `bravo_airport`.`id` and `location_id` = 'CAI') and exists (select * from `bravo_airport` where `bravo_flight`.`airport_to` = `bravo_airport`.`id` and `location_id` = 'JED') and `bravo_flight`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish", "CAI", "JED"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 54}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00447, "duration_str": "4.47ms", "stmt_id": "/modules/Flight/Controllers/FlightController.php:54", "connection": "megafly"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null limit 15", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 75}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "/modules/Flight/Controllers/FlightController.php:75", "connection": "megafly"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 75}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "/modules/Flight/Controllers/FlightController.php:75", "connection": "megafly"}, {"sql": "select * from `bravo_seat_type` where `bravo_seat_type`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 76}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "/modules/Flight/Controllers/FlightController.php:76", "connection": "megafly"}, {"sql": "select min(`price`) as aggregate from `bravo_flight_seat` where exists (select * from `bravo_flight` where `bravo_flight_seat`.`flight_id` = `bravo_flight`.`id` and `status` = 'publish' and `bravo_flight`.`deleted_at` is null) and `bravo_flight_seat`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Flight/Models/Flight.php", "line": 489}, {"index": 16, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 77}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "duration": 0.00155, "duration_str": "1.55ms", "stmt_id": "/modules/Flight/Models/Flight.php:489", "connection": "megafly"}, {"sql": "select max(`price`) as aggregate from `bravo_flight_seat` where exists (select * from `bravo_flight` where `bravo_flight_seat`.`flight_id` = `bravo_flight`.`id` and `status` = 'publish' and `bravo_flight`.`deleted_at` is null) and `bravo_flight_seat`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Flight/Models/Flight.php", "line": 492}, {"index": 16, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 77}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "/modules/Flight/Models/Flight.php:492", "connection": "megafly"}, {"sql": "select * from `bravo_attrs` where `service` = 'flight' and `bravo_attrs`.`deleted_at` is null order by `position` desc", "type": "query", "params": [], "bindings": ["flight"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 95}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00171, "duration_str": "1.71ms", "stmt_id": "/modules/Flight/Controllers/FlightController.php:95", "connection": "megafly"}, {"sql": "select `bravo_terms`.*, (select count(*) from `bravo_flight` inner join `bravo_flight_term` on `bravo_flight`.`id` = `bravo_flight_term`.`target_id` where `bravo_terms`.`id` = `bravo_flight_term`.`term_id` and `bravo_flight`.`deleted_at` is null) as `flight_count` from `bravo_terms` where `bravo_terms`.`attr_id` in (12, 13) and `bravo_terms`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 95}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.0035, "duration_str": "3.5ms", "stmt_id": "/modules/Flight/Controllers/FlightController.php:95", "connection": "megafly"}, {"sql": "select * from `bravo_terms_translations` where `locale` = 'en' and `bravo_terms_translations`.`origin_id` in (83, 84, 85, 86, 87, 88, 89, 90)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 95}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 27, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "/modules/Flight/Controllers/FlightController.php:95", "connection": "megafly"}, {"sql": "select * from `bravo_attrs_translations` where `locale` = 'en' and `bravo_attrs_translations`.`origin_id` in (12, 13)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 95}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "/modules/Flight/Controllers/FlightController.php", "line": 40}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "/modules/Flight/Controllers/FlightController.php:95", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00186, "duration_str": "1.86ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00312, "duration_str": "3.12ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.003, "duration_str": "3ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0024100000000000002, "duration_str": "2.41ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0016, "duration_str": "1.6ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 111}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = 1 and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 36}, {"index": 20, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 19}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 116}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "/modules/Core/Walkers/MenuWalker.php:36", "connection": "megafly"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 111}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = 1 and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 36}, {"index": 20, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 19}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 116}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "/modules/Core/Walkers/MenuWalker.php:36", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = '' and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 627}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "/app/Helpers/AppHelper.php:627", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "2ba402c0f2d314012964e891c18a8c9c9aad769a", "line": 114}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "view::2ba402c0f2d314012964e891c18a8c9c9aad769a:114", "connection": "megafly"}]}, "models": {"data": {"Modules\\Page\\Models\\Page": 2, "Modules\\Core\\Models\\MenuTranslation": 2, "App\\Models\\Airports": 3848, "Modules\\Core\\Models\\Terms": 8, "Modules\\Core\\Models\\Attributes": 2, "Modules\\Flight\\Models\\SeatType": 5, "Modules\\Location\\Models\\Location": 10}, "count": 3877}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/flight?adults=1&children=0&from_where=CAI&infants=0&seat_type=economy&start_oneway=06%2F07%2F2025&start_roundtrip=07%2F07%2F2025&to_where=JED\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/flight", "status_code": "<pre class=sf-dump id=sf-dump-1887063276 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1887063276\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1717467835 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>from_where</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CAI</span>\"\n  \"<span class=sf-dump-key>to_where</span>\" => \"<span class=sf-dump-str title=\"3 characters\">JED</span>\"\n  \"<span class=sf-dump-key>start_oneway</span>\" => \"<span class=sf-dump-str title=\"10 characters\">06/07/2025</span>\"\n  \"<span class=sf-dump-key>start_roundtrip</span>\" => \"<span class=sf-dump-str title=\"10 characters\">07/07/2025</span>\"\n  \"<span class=sf-dump-key>adults</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>children</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>infants</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>seat_type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">economy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1717467835\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-219933841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-219933841\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1856000674 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"163 characters\">http://127.0.0.1:8001/flight?from_where=CAI&amp;to_where=ZNZ&amp;start_oneway=06%2F07%2F2025&amp;start_roundtrip=07%2F07%2F2025&amp;adults=1&amp;children=0&amp;infants=0&amp;seat_type=economy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6Ik1OV052b0ZUckI2TGt2dkdPQVYxckE9PSIsInZhbHVlIjoiM0FUMmtYSXhYK2lHMk9zRkxCL3VwUWtiVG9ibStGZDBvTHRXU3paSzhBSGRyV2svMzQxaGxDY2xKWHlqOXFjS2xlRXV3UDZLUnlNKzNReDdxc1NOaXlrRGZ1YkRsdm0vbENpQU1ablhONGM2L1NnVlpJMDVxcU1OcEtXVnVMK1MiLCJtYWMiOiIyNzA1MWYzYzYwMmM5MTJhOWNiYjQ2ZTNiNTIxYTg3NDg4MjdjZTNkODNlODAyZmM4NDM1OTdlNjFmZWNiM2JiIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlJKZUhSalBwemMxaElYWCs1QlV1Ymc9PSIsInZhbHVlIjoiWGFjckozTGlhbENTUEJEZzNzVUc3MGIvdWphVTVTUk9ZSzNYNEYvZXA2eko0d3dQcUhEMGdKVmdOajJ6UGhSbDI5QVlkVFQwV3FXWi9FNjFEcnRpeFVXS09CbjJDSFZWcHNJMlI5dnUxb1Jaa0YyUXVNV3dUU1ZXQ0RRTTRwckQiLCJtYWMiOiIyNGJhODMwNmYwNzhjNGZhNmJhMzE4OTdhOTYxOTg5MTY3MzYwYTA4MWMxOGQ0N2M0YTg2NDVhMzQ0ZmNhMDdlIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856000674\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-10051541 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">50493</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"142 characters\">/flight?from_where=CAI&amp;to_where=JED&amp;start_oneway=06%2F07%2F2025&amp;start_roundtrip=07%2F07%2F2025&amp;adults=1&amp;children=0&amp;infants=0&amp;seat_type=economy</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"7 characters\">/flight</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/index.php/flight</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"134 characters\">from_where=CAI&amp;to_where=JED&amp;start_oneway=06%2F07%2F2025&amp;start_roundtrip=07%2F07%2F2025&amp;adults=1&amp;children=0&amp;infants=0&amp;seat_type=economy</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"163 characters\">http://127.0.0.1:8001/flight?from_where=CAI&amp;to_where=ZNZ&amp;start_oneway=06%2F07%2F2025&amp;start_roundtrip=07%2F07%2F2025&amp;adults=1&amp;children=0&amp;infants=0&amp;seat_type=economy</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6Ik1OV052b0ZUckI2TGt2dkdPQVYxckE9PSIsInZhbHVlIjoiM0FUMmtYSXhYK2lHMk9zRkxCL3VwUWtiVG9ibStGZDBvTHRXU3paSzhBSGRyV2svMzQxaGxDY2xKWHlqOXFjS2xlRXV3UDZLUnlNKzNReDdxc1NOaXlrRGZ1YkRsdm0vbENpQU1ablhONGM2L1NnVlpJMDVxcU1OcEtXVnVMK1MiLCJtYWMiOiIyNzA1MWYzYzYwMmM5MTJhOWNiYjQ2ZTNiNTIxYTg3NDg4MjdjZTNkODNlODAyZmM4NDM1OTdlNjFmZWNiM2JiIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlJKZUhSalBwemMxaElYWCs1QlV1Ymc9PSIsInZhbHVlIjoiWGFjckozTGlhbENTUEJEZzNzVUc3MGIvdWphVTVTUk9ZSzNYNEYvZXA2eko0d3dQcUhEMGdKVmdOajJ6UGhSbDI5QVlkVFQwV3FXWi9FNjFEcnRpeFVXS09CbjJDSFZWcHNJMlI5dnUxb1Jaa0YyUXVNV3dUU1ZXQ0RRTTRwckQiLCJtYWMiOiIyNGJhODMwNmYwNzhjNGZhNmJhMzE4OTdhOTYxOTg5MTY3MzYwYTA4MWMxOGQ0N2M0YTg2NDVhMzQ0ZmNhMDdlIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751825439.8988</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751825439</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10051541\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-61064899 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61064899\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-911690888 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 18:10:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik4xWURrMEltK3ZBNFVyL1dub1dlZ1E9PSIsInZhbHVlIjoiVitRbVkvNXUwTURBbDRBR2E5Rkt3ZCtXSG9iazJMbzV4NjJpaTRGOVh2SE1OdWhhYWQ5M1A3R3h3RWRIL3VDK1NDMWlrK3R1U1RaRmo2aktzZHlNUGpQWGtkMWw0ekhOVXZnYnlNL3pMdFMxSnc2WWphak1pSGNZRWpsU1kyaDMiLCJtYWMiOiI0MDVjZWRmZmFmZTQ2MmQ0ZjEyNTEyNDAyYjQ0MWQ4ZjM2YWI0MTBjOTkxYWYxMzk4OTU0YjFlYjc5ZTRmMDQ3IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:10:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6InVXTlJNdmpMK2kya1FYWGZSb2JvSUE9PSIsInZhbHVlIjoiRVA2dXYrR2VPeGtXSTB4Y3ROWFpITFEwQW5DeDlFTmttQVRtbVhZQXZyM0J0RXBBOGVNQ3E0OW9FTDk2U21WSTlXSnNxbGVpSnRsQVFMWEUyVjRGMytaS1VxeWJubUREd2dJK1JOYVhwZjR2aFIweXU5T3pEdnljT1ZKQmdmbGYiLCJtYWMiOiI3Y2YxZTc5MDNiYWY4MTk2YjU5ZDIxYTlmODFlOWI2OTMzMWI1YzE0NzlmNDAyNWQxNmNiYTIxZjA4NTg0NjYwIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:10:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik4xWURrMEltK3ZBNFVyL1dub1dlZ1E9PSIsInZhbHVlIjoiVitRbVkvNXUwTURBbDRBR2E5Rkt3ZCtXSG9iazJMbzV4NjJpaTRGOVh2SE1OdWhhYWQ5M1A3R3h3RWRIL3VDK1NDMWlrK3R1U1RaRmo2aktzZHlNUGpQWGtkMWw0ekhOVXZnYnlNL3pMdFMxSnc2WWphak1pSGNZRWpsU1kyaDMiLCJtYWMiOiI0MDVjZWRmZmFmZTQ2MmQ0ZjEyNTEyNDAyYjQ0MWQ4ZjM2YWI0MTBjOTkxYWYxMzk4OTU0YjFlYjc5ZTRmMDQ3IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:10:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6InVXTlJNdmpMK2kya1FYWGZSb2JvSUE9PSIsInZhbHVlIjoiRVA2dXYrR2VPeGtXSTB4Y3ROWFpITFEwQW5DeDlFTmttQVRtbVhZQXZyM0J0RXBBOGVNQ3E0OW9FTDk2U21WSTlXSnNxbGVpSnRsQVFMWEUyVjRGMytaS1VxeWJubUREd2dJK1JOYVhwZjR2aFIweXU5T3pEdnljT1ZKQmdmbGYiLCJtYWMiOiI3Y2YxZTc5MDNiYWY4MTk2YjU5ZDIxYTlmODFlOWI2OTMzMWI1YzE0NzlmNDAyNWQxNmNiYTIxZjA4NTg0NjYwIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:10:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911690888\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1607191152 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"163 characters\">http://127.0.0.1:8001/flight?adults=1&amp;children=0&amp;from_where=CAI&amp;infants=0&amp;seat_type=economy&amp;start_oneway=06%2F07%2F2025&amp;start_roundtrip=07%2F07%2F2025&amp;to_where=JED</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1607191152\", {\"maxDepth\":0})</script>\n"}}