{"__meta": {"id": "X0bbb1e98364dddfd14b14f279b2e6995", "datetime": "2025-07-06 17:51:40", "utime": 1751824300.412321, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[17:51:40] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751824300.410151, "collector": "log"}, {"message": "[17:51:40] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751824300.410164, "collector": "log"}, {"message": "[17:51:40] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751824300.410274, "collector": "log"}, {"message": "[17:51:40] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751824300.410292, "collector": "log"}]}, "time": {"start": 1751824300.387171, "end": 1751824300.41233, "duration": 0.02515888214111328, "duration_str": "25.16ms", "measures": [{"label": "Booting", "start": 1751824300.387171, "relative_start": 0, "end": 1751824300.409881, "relative_end": 1751824300.409881, "duration": 0.022710084915161133, "duration_str": "22.71ms", "params": [], "collector": null}, {"label": "Application", "start": 1751824300.409996, "relative_start": 0.022825002670288086, "end": 1751824300.41233, "relative_end": 0, "duration": 0.0023338794708251953, "duration_str": "2.33ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2731592, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-1978325689 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1978325689\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1862223533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1862223533\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1245407356 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1245407356\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1625943074 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6Ik93OXMvZXBCOVh2Y1F3cXNhVVNrN3c9PSIsInZhbHVlIjoiV1ZnT1hFakRIb1Fsaml0TUp4K05CWTRNZElVaDhtUndGbzBvS0pBdGVtcjl6dWpreDBqcEdjd3VpS1NjOExGUXpHYiszbERxdEFyVEsyaEFybWJROHJRTU5HUldCSjR1dTJnd1gwdkozS1hqL2Yydm1nenloNm81UENCSFlXRG8iLCJtYWMiOiJlMTcyMTJjZjhmMmUxMGU1MmI2OTExMDUwMGU3ZjYwMzFjZGQxZmFmYTdkZTQxNTg3MTZiNzI5YThkM2Q4NGFjIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjlNdDNiMmtCWmRsRWNDNnVPbHU1aWc9PSIsInZhbHVlIjoiS2M0SlJsM01RTEFDQXN4bm1RYXA0T1gvMlFQY2FFclpzS1pna1Zid0x1NVE3MXNMcVJCc0t6amtiVEs1cVBTeFJ4dlNYUHY5a3k3NktFYlp6R1JDVzNkbVNKNUZDY3JZSXk3aUFaMGhkVm5POTdVRnNnVkpnV2RyWkhWcHdsUkIiLCJtYWMiOiI5YmY3MWMzMjlhM2QxNDdmOGMxMGVhNWYxNmI0N2E2YWZjZDUzNDRmNzk2OWIxYTkwYWNkOGExNjdhY2EzZDcwIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1625943074\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1658617586 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59510</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6Ik93OXMvZXBCOVh2Y1F3cXNhVVNrN3c9PSIsInZhbHVlIjoiV1ZnT1hFakRIb1Fsaml0TUp4K05CWTRNZElVaDhtUndGbzBvS0pBdGVtcjl6dWpreDBqcEdjd3VpS1NjOExGUXpHYiszbERxdEFyVEsyaEFybWJROHJRTU5HUldCSjR1dTJnd1gwdkozS1hqL2Yydm1nenloNm81UENCSFlXRG8iLCJtYWMiOiJlMTcyMTJjZjhmMmUxMGU1MmI2OTExMDUwMGU3ZjYwMzFjZGQxZmFmYTdkZTQxNTg3MTZiNzI5YThkM2Q4NGFjIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjlNdDNiMmtCWmRsRWNDNnVPbHU1aWc9PSIsInZhbHVlIjoiS2M0SlJsM01RTEFDQXN4bm1RYXA0T1gvMlFQY2FFclpzS1pna1Zid0x1NVE3MXNMcVJCc0t6amtiVEs1cVBTeFJ4dlNYUHY5a3k3NktFYlp6R1JDVzNkbVNKNUZDY3JZSXk3aUFaMGhkVm5POTdVRnNnVkpnV2RyWkhWcHdsUkIiLCJtYWMiOiI5YmY3MWMzMjlhM2QxNDdmOGMxMGVhNWYxNmI0N2E2YWZjZDUzNDRmNzk2OWIxYTkwYWNkOGExNjdhY2EzZDcwIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751824300.3872</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751824300</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658617586\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-479494950 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>royaltransit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-479494950\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-285323653 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 17:51:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlUvL2VFeHVkQlJQY2FiVDRRbGg2N0E9PSIsInZhbHVlIjoiS0U2cDM4V2cxVjJscGgyMEwzYlcrUDRzemszMmJObDJ4MUdkc25hZVpvL2VkYWlJcE82Nlg5RUx6UThqdmJRclQwTjNuRGp0Q1AyeWFSb1RUejIvUFZoNy9OenAwMm1ERHp1VFc2dnBYYU5DZEVOWnNxSHJLRXRIVXlJNTUzSnMiLCJtYWMiOiI5NWYzNzE5MjRmOTFlODhlYzk5YjEwYjBhYjIyNTdjN2NlMGUyZjc0NjA1YzMwN2UwMmZmNzM2NzBlZGQ4ZDM4IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 19:51:40 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6InduNmplSEF6blRjckloTVZTRFRPVVE9PSIsInZhbHVlIjoiVjJmdXlTOUZxOXJvdG82RGpJQnoxRjY2Wk95NGQvZklndzlpZFhEMlpoQ2ZPS2NNdm9KTEFLc2kwVUo2aGxnSFk0SFpQdTBRbVBIZVl2RWxJM2ZsWDFOQ0dwclRpTGhUUU1sblpXbk0rc0V4b09BQS9QWEJmV05NM0RvVzV2U2UiLCJtYWMiOiJiM2I3N2QzNWMxMDY1Y2U5ODZhZDNhNTVmNzc4Y2VjZTg2NDJiODgyYzIzZjA4ZmJkMWI1NmE5MDhhMDllMGI0IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 19:51:40 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlUvL2VFeHVkQlJQY2FiVDRRbGg2N0E9PSIsInZhbHVlIjoiS0U2cDM4V2cxVjJscGgyMEwzYlcrUDRzemszMmJObDJ4MUdkc25hZVpvL2VkYWlJcE82Nlg5RUx6UThqdmJRclQwTjNuRGp0Q1AyeWFSb1RUejIvUFZoNy9OenAwMm1ERHp1VFc2dnBYYU5DZEVOWnNxSHJLRXRIVXlJNTUzSnMiLCJtYWMiOiI5NWYzNzE5MjRmOTFlODhlYzk5YjEwYjBhYjIyNTdjN2NlMGUyZjc0NjA1YzMwN2UwMmZmNzM2NzBlZGQ4ZDM4IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 19:51:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6InduNmplSEF6blRjckloTVZTRFRPVVE9PSIsInZhbHVlIjoiVjJmdXlTOUZxOXJvdG82RGpJQnoxRjY2Wk95NGQvZklndzlpZFhEMlpoQ2ZPS2NNdm9KTEFLc2kwVUo2aGxnSFk0SFpQdTBRbVBIZVl2RWxJM2ZsWDFOQ0dwclRpTGhUUU1sblpXbk0rc0V4b09BQS9QWEJmV05NM0RvVzV2U2UiLCJtYWMiOiJiM2I3N2QzNWMxMDY1Y2U5ODZhZDNhNTVmNzc4Y2VjZTg2NDJiODgyYzIzZjA4ZmJkMWI1NmE5MDhhMDllMGI0IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 19:51:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285323653\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2078223075 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078223075\", {\"maxDepth\":0})</script>\n"}}