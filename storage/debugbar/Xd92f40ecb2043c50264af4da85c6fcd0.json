{"__meta": {"id": "Xd92f40ecb2043c50264af4da85c6fcd0", "datetime": "2025-07-06 14:33:16", "utime": 1751812396.633957, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:33:16] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812396.625419, "collector": "log"}, {"message": "[14:33:16] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812396.625432, "collector": "log"}, {"message": "[14:33:16] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812396.625555, "collector": "log"}, {"message": "[14:33:16] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812396.625569, "collector": "log"}]}, "time": {"start": 1751812396.588516, "end": 1751812396.633966, "duration": 0.04544997215270996, "duration_str": "45.45ms", "measures": [{"label": "Booting", "start": 1751812396.588516, "relative_start": 0, "end": 1751812396.625066, "relative_end": 1751812396.625066, "duration": 0.036550045013427734, "duration_str": "36.55ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812396.625211, "relative_start": 0.036695003509521484, "end": 1751812396.633967, "relative_end": 9.5367431640625e-07, "duration": 0.008755922317504883, "duration_str": "8.76ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3217896, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0049, "accumulated_duration_str": "4.9ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00335, "duration_str": "3.35ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` = 'closed' and `updated_at` >= '2025-07-06 14:23:16' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "closed", "2025-07-06 14:23:16"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00155, "duration_str": "1.55ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-920698488 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-920698488\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-797744055 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IklLL1hwMlE0QzNRdEJuRTNCbnpscnc9PSIsInZhbHVlIjoid3BPZDRYem5GMHJaRmIyV2h1dXVETnB5bnl5enVOS0N4MDNscGFxODhwZDQ5WWg1OW9WZVlUTWNmM2x2YTgyNTlDbUpXTkNSdFlHT0lEb0lIZWhVNmg0U1R1L25rdGhmYXc1bjZpUzE4MkZDeTBTdDNuNi8rU1dCU3RzTkQxcFkiLCJtYWMiOiIzMTJmNDQ2NDA0Mjk1YWRkYmQ4M2RiOTJlNzljYTVhMjAxN2YwMmNjYTc3YzQ3ZGY5OTcwYjE3Y2IxZTFmNDQ5IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Imk4ZjBsbHhCcW8ra1pISmZ0bTF1eVE9PSIsInZhbHVlIjoiTkFpNi9TQkNWbDZXOWhPa3NwQWtEWUxQR05Kb1llT3BIempvbGNlWG5NcVlVNml1R3lMNFRzb29aVzZHMmpjODd1VStkVysvcDc3WEI2UXR0c0xhZ2NOejRXYTBsWnErUnVCemh4YWdmVVhnclZhenBIZ0JVSk4vTnhMMUxBZjIiLCJtYWMiOiJjNDI0M2YzYmZjY2E0MTcyOGFiMzc2ODg2Mjk1NmRiMjI1Yjg0NjI2YmIxNzYwY2MzZTlmNjU0ODc0YjljYTRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797744055\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1286034016 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58938</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IklLL1hwMlE0QzNRdEJuRTNCbnpscnc9PSIsInZhbHVlIjoid3BPZDRYem5GMHJaRmIyV2h1dXVETnB5bnl5enVOS0N4MDNscGFxODhwZDQ5WWg1OW9WZVlUTWNmM2x2YTgyNTlDbUpXTkNSdFlHT0lEb0lIZWhVNmg0U1R1L25rdGhmYXc1bjZpUzE4MkZDeTBTdDNuNi8rU1dCU3RzTkQxcFkiLCJtYWMiOiIzMTJmNDQ2NDA0Mjk1YWRkYmQ4M2RiOTJlNzljYTVhMjAxN2YwMmNjYTc3YzQ3ZGY5OTcwYjE3Y2IxZTFmNDQ5IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Imk4ZjBsbHhCcW8ra1pISmZ0bTF1eVE9PSIsInZhbHVlIjoiTkFpNi9TQkNWbDZXOWhPa3NwQWtEWUxQR05Kb1llT3BIempvbGNlWG5NcVlVNml1R3lMNFRzb29aVzZHMmpjODd1VStkVysvcDc3WEI2UXR0c0xhZ2NOejRXYTBsWnErUnVCemh4YWdmVVhnclZhenBIZ0JVSk4vTnhMMUxBZjIiLCJtYWMiOiJjNDI0M2YzYmZjY2E0MTcyOGFiMzc2ODg2Mjk1NmRiMjI1Yjg0NjI2YmIxNzYwY2MzZTlmNjU0ODc0YjljYTRlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812396.5885</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812396</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286034016\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1869427363 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869427363\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:33:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjU4Y0g0djhkdzZkT2hVVGw4UXBZbWc9PSIsInZhbHVlIjoiTkZZQnU0WFV3UjBUZDM4d3FvZDJHSlVIQTBBaC9WK1pOdnVkeFJVYUI1TjdUQkZ6Q1cxekozYlI0ZTVzK1RlaGJwRzIvOWxHUGtWL2hGQkVWYWlmYVQvWkFKNXVpUFhFT1ZMMkVta3JUMFh6L29kbEN3NWlKNC9qd1pqLzYvSFMiLCJtYWMiOiJkMTZjMDg2NzFkOGNkOGNjM2M1NDRkYmFlNTJlYTEzYTJkYjdlYjRhNzM4Y2M0NzMyYjA1MTRmNjFlZTAxZDViIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IiswUDh3K3RSYWwyeXMraFJCSW9qRmc9PSIsInZhbHVlIjoiRlBEeFRja1QvSXZoWE14U1hqeURha0xpMWpsNWhDQTJrNFNhd2FncVFFaFpMUWJTalFwQzM4enZGQ2pLOHkrYi9lUnhLcjdoeWJaZFVZamRoSUhZYmJKWG1uaHB0bHRZeWxpU01icnFNMDI0blRINDFWcWl1QU5NaG5RL001WWMiLCJtYWMiOiJiZjI2ZjQ4Njk3NGEwZmUyMzk3M2ZlYjhhNzcwYzBhZmEzOGZhMWJmNDE0Yjc0YTNmZGRhYzcyNDNhM2I1M2U5IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjU4Y0g0djhkdzZkT2hVVGw4UXBZbWc9PSIsInZhbHVlIjoiTkZZQnU0WFV3UjBUZDM4d3FvZDJHSlVIQTBBaC9WK1pOdnVkeFJVYUI1TjdUQkZ6Q1cxekozYlI0ZTVzK1RlaGJwRzIvOWxHUGtWL2hGQkVWYWlmYVQvWkFKNXVpUFhFT1ZMMkVta3JUMFh6L29kbEN3NWlKNC9qd1pqLzYvSFMiLCJtYWMiOiJkMTZjMDg2NzFkOGNkOGNjM2M1NDRkYmFlNTJlYTEzYTJkYjdlYjRhNzM4Y2M0NzMyYjA1MTRmNjFlZTAxZDViIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IiswUDh3K3RSYWwyeXMraFJCSW9qRmc9PSIsInZhbHVlIjoiRlBEeFRja1QvSXZoWE14U1hqeURha0xpMWpsNWhDQTJrNFNhd2FncVFFaFpMUWJTalFwQzM4enZGQ2pLOHkrYi9lUnhLcjdoeWJaZFVZamRoSUhZYmJKWG1uaHB0bHRZeWxpU01icnFNMDI0blRINDFWcWl1QU5NaG5RL001WWMiLCJtYWMiOiJiZjI2ZjQ4Njk3NGEwZmUyMzk3M2ZlYjhhNzcwYzBhZmEzOGZhMWJmNDE0Yjc0YTNmZGRhYzcyNDNhM2I1M2U5IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1269914384 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1269914384\", {\"maxDepth\":0})</script>\n"}}