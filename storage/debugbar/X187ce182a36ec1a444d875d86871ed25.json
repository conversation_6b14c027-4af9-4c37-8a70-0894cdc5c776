{"__meta": {"id": "X187ce182a36ec1a444d875d86871ed25", "datetime": "2025-07-06 14:31:31", "utime": 1751812291.432899, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 16, "messages": [{"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.325062, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.325077, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.325221, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.325236, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_AttrTransform_NameSync::$idDef is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.418237, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_AttrTransform_NameSync::$idDef is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.418356, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.418535, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.418806, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.421791, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.42191, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.422411, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_ChildDef_List::$whitespace is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/List.php on line 34", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.422785, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.423378, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.424187, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.424519, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property HTMLPurifier_Lexer_DOMLex::$_entity_parser is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.424781, "collector": "log"}]}, "time": {"start": 1751812291.286788, "end": 1751812291.432911, "duration": 0.14612293243408203, "duration_str": "146ms", "measures": [{"label": "Booting", "start": 1751812291.286788, "relative_start": 0, "end": 1751812291.324658, "relative_end": 1751812291.324658, "duration": 0.037869930267333984, "duration_str": "37.87ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812291.324819, "relative_start": 0.03803110122680664, "end": 1751812291.432912, "relative_end": 1.1920928955078125e-06, "duration": 0.1080930233001709, "duration_str": "108ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 8210184, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 43, "templates": [{"name": "Page::frontend.detail (themes/Mytravel/Page/Views/frontend/detail.blade.php)", "param_count": 4, "params": ["row", "seo_meta", "translation", "is_home"], "type": "blade"}, {"name": "Template::frontend.blocks.form-search-all-service.style_1 (themes/Mytravel/Template/Views/frontend/blocks/form-search-all-service/style_1.blade.php)", "param_count": 19, "params": ["service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.form-search (themes/Mytravel/Flight/Views/frontend/layouts/search/form-search.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 28, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "return"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.from-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/from-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.to-where-flight (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Flight::frontend.layouts.search.fields.date-single (themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php)", "param_count": 30, "params": ["__env", "app", "errors", "service_types", "title", "sub_title", "bg_image", "style", "list_slider", "title_for_car", "title_for_event", "title_for_space", "title_for_hotel", "title_for_tour", "hide_form_search", "title_for_flight", "single_form_search", "bg_image_url", "tour_location", "list_location", "modelBlock", "seatType", "number", "__currentLoopData", "service_type", "loop", "allServices", "module", "i", "multiway_index"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.style_1 (themes/Mytravel/Location/Views/frontend/blocks/list-locations/style_1.blade.php)", "param_count": 6, "params": ["rows", "title", "desc", "service_type", "layout", "to_location_detail"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "Location::frontend.blocks.list-locations.loop (themes/Mytravel/Location/Views/frontend/blocks/list-locations/loop.blade.php)", "param_count": 14, "params": ["__env", "app", "errors", "rows", "title", "desc", "service_type", "layout", "to_location_detail", "__currentLoopData", "row", "key", "loop", "class"], "type": "blade"}, {"name": "layouts.app (resources/views/layouts/app.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home"], "type": "blade"}, {"name": "Layout::app (themes/Mytravel/Layout/app.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home"], "type": "blade"}, {"name": "Layout::parts.seo-meta (themes/Mytravel/Layout/parts/seo-meta.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.global-script (modules/Layout/parts/global-script.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.header (themes/Mytravel/Layout/parts/header.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.topbar (themes/Mytravel/Layout/parts/topbar.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Core::frontend.currency-switcher (themes/Mytravel/Core/Views/frontend/currency-switcher.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Language::frontend.switcher (themes/Mytravel/Language/Views/frontend/switcher.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Layout::parts.notification (themes/Mytravel/Layout/parts/notification.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Core::frontend.currency-switcher-dropdown (themes/Mytravel/Core/Views/frontend/currency-switcher-dropdown.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "logo_id", "logo"], "type": "blade"}, {"name": "Language::frontend.switcher-dropdown (themes/Mytravel/Language/Views/frontend/switcher-dropdown.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "logo_id", "logo"], "type": "blade"}, {"name": "Layout::parts.footer (themes/Mytravel/Layout/parts/footer.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file"], "type": "blade"}, {"name": "Language::frontend.switcher (themes/Mytravel/Language/Views/frontend/switcher.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Core::frontend.currency-switcher (themes/Mytravel/Core/Views/frontend/currency-switcher.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::parts.login-register-modal (themes/Mytravel/Layout/parts/login-register-modal.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::auth.login-form (modules/Layout/auth/login-form.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Layout::auth.register-form (modules/Layout/auth/register-form.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Popup::frontend.popup (themes/Mytravel/Popup/Views/frontend/popup.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "info_contact", "list_widget_footers", "__currentLoopData", "item", "key", "loop"], "type": "blade"}, {"name": "Support::frontend.chat.widget (modules/Support/Views/frontend/chat/widget.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "chatConversation", "hasActiveChat", "chatMessages", "sessionId", "conversation", "messages"], "type": "blade"}, {"name": "demo_script (resources/views/demo_script.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "row", "seo_meta", "translation", "is_home", "favicon", "file", "chatConversation", "hasActiveChat", "chatMessages", "sessionId"], "type": "blade"}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/app/Http/Controllers/HomeController.php&line=30\">app/Http/Controllers/HomeController.php:30-60</a>"}, "queries": {"nb_statements": 30, "nb_failed_statements": 0, "accumulated_duration": 0.043039999999999995, "accumulated_duration_str": "43.04ms", "statements": [{"sql": "select * from `core_pages` where `id` = '1' and `status` = 'publish' and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1", "publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 33}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "duration": 0.00364, "duration_str": "3.64ms", "stmt_id": "/app/Http/Controllers/HomeController.php:33", "connection": "megafly"}, {"sql": "select * from `core_page_translations` where `core_page_translations`.`origin_id` = 1 and `core_page_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 36}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `bravo_seo` where `object_id` = 1 and `object_model` = 'page' limit 1", "type": "query", "params": [], "bindings": ["1", "page"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/app/BaseModel.php", "line": 199}, {"index": 16, "namespace": null, "name": "/app/BaseModel.php", "line": 212}, {"index": 17, "namespace": null, "name": "/app/Http/Controllers/HomeController.php", "line": 37}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "/app/BaseModel.php:199", "connection": "megafly"}, {"sql": "select * from `core_templates` where `core_templates`.`id` = 1 and `core_templates`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 79}, {"index": 21, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "/modules/Page/Models/Page.php:79", "connection": "megafly"}, {"sql": "select * from `core_template_translations` where `core_template_translations`.`origin_id` = 1 and `core_template_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 81}, {"index": 22, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `name` asc limit 1000", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php", "line": 131}, {"index": 17, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 18, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 21, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php:131", "connection": "megafly"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php", "line": 131}, {"index": 22, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 23, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php:131", "connection": "megafly"}, {"sql": "select * from `bravo_seat_type` where `bravo_seat_type`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php", "line": 135}, {"index": 20, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 21, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "/themes/Mytravel/Template/Blocks/FormSearchAllService.php:135", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00304, "duration_str": "3.04ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00307, "duration_str": "3.07ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00228, "duration_str": "2.28ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00312, "duration_str": "3.12ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00155, "duration_str": "1.55ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0016200000000000001, "duration_str": "1.62ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0016699999999999998, "duration_str": "1.67ms", "stmt_id": "view::a9c8b4212f56e8a6b29b7f8da50e9fdf130ee71a:10", "connection": "megafly"}, {"sql": "select * from `airports`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "088a6a183e78be4d57757f2404018a3c8b6d680f", "line": 10}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.0031, "duration_str": "3.1ms", "stmt_id": "view::088a6a183e78be4d57757f2404018a3c8b6d680f:10", "connection": "megafly"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `id` asc limit 6", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 159}, {"index": 15, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 126}, {"index": 18, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 19, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 21, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}], "duration": 0.0014199999999999998, "duration_str": "1.42ms", "stmt_id": "/themes/Mytravel/Location/Blocks/ListLocations.php:159", "connection": "megafly"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 159}, {"index": 20, "namespace": null, "name": "/themes/Mytravel/Location/Blocks/ListLocations.php", "line": 126}, {"index": 23, "namespace": null, "name": "/modules/Page/Models/Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "b661a974e5af1b6d80b51ae40cb72c799fb56cbf", "line": 5}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "/themes/Mytravel/Location/Blocks/ListLocations.php:159", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00121, "duration_str": "1.21ms", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Hotel/Models/Hotel.php", "line": 730}, {"index": 16, "namespace": null, "name": "/modules/Location/Models/Location.php", "line": 77}, {"index": 17, "namespace": "view", "name": "15d99b72a71cb13af7dbba871930abf33b3e76aa", "line": 25}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "/modules/Hotel/Models/Hotel.php:730", "connection": "megafly"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 111}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.0011899999999999999, "duration_str": "1.19ms", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = 1 and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 36}, {"index": 20, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 19}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 116}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.00114, "duration_str": "1.14ms", "stmt_id": "/modules/Core/Walkers/MenuWalker.php:36", "connection": "megafly"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "/app/Traits/HasTranslations.php", "line": 51}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 111}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "/app/Traits/HasTranslations.php:51", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = 1 and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 36}, {"index": 20, "namespace": null, "name": "/modules/Core/Walkers/MenuWalker.php", "line": 19}, {"index": 21, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 116}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "/modules/Core/Walkers/MenuWalker.php:36", "connection": "megafly"}, {"sql": "select * from `core_pages` where `core_pages`.`id` = '' and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [""], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "/app/Helpers/AppHelper.php", "line": 627}, {"index": 22, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "/app/Helpers/AppHelper.php:627", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "2ba402c0f2d314012964e891c18a8c9c9aad769a", "line": 114}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "line": 110}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "line": 70}, {"index": 20, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/View/View.php", "line": 152}], "duration": 0.00131, "duration_str": "1.31ms", "stmt_id": "view::2ba402c0f2d314012964e891c18a8c9c9aad769a:114", "connection": "megafly"}]}, "models": {"data": {"Modules\\Core\\Models\\MenuTranslation": 2, "App\\Models\\Airports": 3848, "Modules\\Flight\\Models\\SeatType": 5, "Modules\\Location\\Models\\Location": 16, "Modules\\Template\\Models\\TemplateTranslation": 1, "Modules\\Template\\Models\\Template": 1, "Modules\\Core\\Models\\SEO": 1, "Modules\\Page\\Models\\PageTranslation": 1, "Modules\\Page\\Models\\Page": 3}, "count": 3878}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1489665834 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1489665834\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1710813658 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1710813658\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1692053656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1692053656\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-227968477 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IkRpNEVKVEF4SmRLOUQzbGkvSkZFQXc9PSIsInZhbHVlIjoia1JWaXBKQ0p4L3VxVWh4eDNTVVluL1dyM0RCMElSdXZxNFFlL0VMYlBDV3VBMFpncUh1aWwzODJ1a1oxV2tlOHlOdGllajNyaHd2bHZTY1dTR3U4S0V1aUJNbG5qdk01NmY0YnppVklPQ2JaM0pLaU5BRUY4VGpLVWFFaEJORTYiLCJtYWMiOiI0NDhkOGUzYTk5YTZmZDA4MzE1M2RhZDkxYmI2YmEyN2IyY2RkYzFjZDE5MWNmNzcxZWI3ODg2MWUyNzlmMTAyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkZPV3ZLUk9wNFl3VEhPR2hCQzFpZWc9PSIsInZhbHVlIjoiODhVSW4yYjJBa0VBRk9lYXlhNGFrcHlKQUZCUDNZUlYxZzBaNEZZbFQ3UTRhVXNPV0Jxa3Q1OHVPQlFnYmdJQ0F2Q01TUXVIMXMxTWFvcWp5ZGswTzU5c3dxZW8xeGllUGpQRFFsMmFvcUdPMUpaMkUwQnQySnI1VXV6bVR2dHkiLCJtYWMiOiI5YTlkMTY0NjFkZjJjNTRjN2EzYmZjYWNiOGRkZDZjODQ2MzJjZDBjMmUzNDg3MjAyNzRhN2UzMjMzOTljMjI1IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227968477\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-2121865069 data-indent-pad=\"  \"><span class=sf-dump-note>array:26</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58553</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IkRpNEVKVEF4SmRLOUQzbGkvSkZFQXc9PSIsInZhbHVlIjoia1JWaXBKQ0p4L3VxVWh4eDNTVVluL1dyM0RCMElSdXZxNFFlL0VMYlBDV3VBMFpncUh1aWwzODJ1a1oxV2tlOHlOdGllajNyaHd2bHZTY1dTR3U4S0V1aUJNbG5qdk01NmY0YnppVklPQ2JaM0pLaU5BRUY4VGpLVWFFaEJORTYiLCJtYWMiOiI0NDhkOGUzYTk5YTZmZDA4MzE1M2RhZDkxYmI2YmEyN2IyY2RkYzFjZDE5MWNmNzcxZWI3ODg2MWUyNzlmMTAyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkZPV3ZLUk9wNFl3VEhPR2hCQzFpZWc9PSIsInZhbHVlIjoiODhVSW4yYjJBa0VBRk9lYXlhNGFrcHlKQUZCUDNZUlYxZzBaNEZZbFQ3UTRhVXNPV0Jxa3Q1OHVPQlFnYmdJQ0F2Q01TUXVIMXMxTWFvcWp5ZGswTzU5c3dxZW8xeGllUGpQRFFsMmFvcUdPMUpaMkUwQnQySnI1VXV6bVR2dHkiLCJtYWMiOiI5YTlkMTY0NjFkZjJjNTRjN2EzYmZjYWNiOGRkZDZjODQ2MzJjZDBjMmUzNDg3MjAyNzRhN2UzMjMzOTljMjI1IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812291.2868</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812291</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121865069\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1737699005 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737699005\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-52806034 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:31:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InYwSkdCcC9FWkxqRE9IdVFUZ0d1eHc9PSIsInZhbHVlIjoiOTliM0JLNnBlSStSYVFwSzVBVXk1VnFicXN1N2NmSE9SbGFQdW4vcGl4cjBNUVpZOUxQSVpLZERpQzI3N3pJU3BOWkdkekNzNUEzelZ0b1V4RFdzbVRNdCs2MVUweHc4UmdSViszSk5zUk1VOHA2Qk5CL3Y2S2tVbnRUV0k3L3IiLCJtYWMiOiIwNjY4YTFkYzcyMTE4MWIxNTkxZDY1M2U4ZTAyNzFjMGJiYWViOWE1NjZmY2UzN2I1NjkwNGU3NGU4MTczMDUyIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IkRjdWpjV3JQY3BzL0IvT0hzUTBuTmc9PSIsInZhbHVlIjoiaWJNYXlQSHN6dU4rMXNXc256T0I3OXJsd0Fxc0JmbUY1OVZyL3JCaHROem1ROXFUd3V4c1BySk96WXNHL1ZSb2hJcXZoeTRPalEyYVNKTVllSm9UNTJhUWdLV1hKVEtyZElNUnFIZWFkNERyai92VXJoSm9JdVRPQkYwYk93ZjciLCJtYWMiOiJlNGM5NmY1ZjAwOGU3YTMwMDI1NGE4NTA0ODgwNGFjZTBkZjVlMGE1MTg4MTFlMDAyMjQ4MTUwNDU3OWU5NjFkIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InYwSkdCcC9FWkxqRE9IdVFUZ0d1eHc9PSIsInZhbHVlIjoiOTliM0JLNnBlSStSYVFwSzVBVXk1VnFicXN1N2NmSE9SbGFQdW4vcGl4cjBNUVpZOUxQSVpLZERpQzI3N3pJU3BOWkdkekNzNUEzelZ0b1V4RFdzbVRNdCs2MVUweHc4UmdSViszSk5zUk1VOHA2Qk5CL3Y2S2tVbnRUV0k3L3IiLCJtYWMiOiIwNjY4YTFkYzcyMTE4MWIxNTkxZDY1M2U4ZTAyNzFjMGJiYWViOWE1NjZmY2UzN2I1NjkwNGU3NGU4MTczMDUyIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IkRjdWpjV3JQY3BzL0IvT0hzUTBuTmc9PSIsInZhbHVlIjoiaWJNYXlQSHN6dU4rMXNXc256T0I3OXJsd0Fxc0JmbUY1OVZyL3JCaHROem1ROXFUd3V4c1BySk96WXNHL1ZSb2hJcXZoeTRPalEyYVNKTVllSm9UNTJhUWdLV1hKVEtyZElNUnFIZWFkNERyai92VXJoSm9JdVRPQkYwYk93ZjciLCJtYWMiOiJlNGM5NmY1ZjAwOGU3YTMwMDI1NGE4NTA0ODgwNGFjZTBkZjVlMGE1MTg4MTFlMDAyMjQ4MTUwNDU3OWU5NjFkIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52806034\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1353032731 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353032731\", {\"maxDepth\":0})</script>\n"}}