{"__meta": {"id": "X8c2193c664090c1a02a8bd1960d4649b", "datetime": "2025-07-06 18:08:12", "utime": 1751825292.48105, "method": "GET", "uri": "/location/search/searchForSelect2?search=he", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[18:08:12] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825292.472722, "collector": "log"}, {"message": "[18:08:12] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825292.472737, "collector": "log"}, {"message": "[18:08:12] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825292.472842, "collector": "log"}, {"message": "[18:08:12] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825292.472858, "collector": "log"}]}, "time": {"start": 1751825292.448778, "end": 1751825292.481064, "duration": 0.03228616714477539, "duration_str": "32.29ms", "measures": [{"label": "Booting", "start": 1751825292.448778, "relative_start": 0, "end": 1751825292.472444, "relative_end": 1751825292.472444, "duration": 0.0236661434173584, "duration_str": "23.67ms", "params": [], "collector": null}, {"label": "Application", "start": 1751825292.472562, "relative_start": 0.023784160614013672, "end": 1751825292.481065, "relative_end": 9.5367431640625e-07, "duration": 0.008502960205078125, "duration_str": "8.5ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3324696, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET location/search/searchForSelect2", "middleware": "web", "controller": "Modules\\Location\\Controllers\\LocationController@searchForSelect2", "namespace": "Modules\\Location\\Controllers", "prefix": "/location", "where": [], "as": "location.searchForSelect", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Location/Controllers/LocationController.php&line=44\">modules/Location/Controllers/LocationController.php:44-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00436, "accumulated_duration_str": "4.36ms", "statements": [{"sql": "select `airports`.*, `airports`.`name` as `title` from `airports` where `airports`.`name` like '%he%' or `airports`.`code` like '%he%' order by `name` asc limit 20", "type": "query", "params": [], "bindings": ["%he%", "%he%"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/modules/Location/Controllers/LocationController.php", "line": 61}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "duration": 0.00436, "duration_str": "4.36ms", "stmt_id": "/modules/Location/Controllers/LocationController.php:61", "connection": "megafly"}]}, "models": {"data": {"App\\Models\\Airports": 20}, "count": 20}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/location/search/searchForSelect2", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">he</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1626470200 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1626470200\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-357134195 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IlRWbDJHZzBMMEZxb0t0amZNblRCdGc9PSIsInZhbHVlIjoiYWlhc0llMjd5aFIvV3lENWZwdE4rQzFmRmN0Z2k0L2dmV3JRWDlhbnBNOHBNVURTQmJCWmdudmdKM2pqenB2amVzNXdjOGl5dDRSVUF0RnNLWVhtTlBMRnRSTngvQVRmSFNNdGlDcjBwVGg2UFI1ZDBlVGJxaHpKeURldFgrUWkiLCJtYWMiOiIyZDc1NDBhYzNkNzRkZjFlYzE0NTcyZWU2ZWNhZDJjNzk1ZGU2ZjI0ZTlkN2Q1NDEyMThmMzZlYjkwNjViNmMwIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ink4K1I2cFpLMlJlbC9CbTBQZWdBcGc9PSIsInZhbHVlIjoiU2ZtMmdldUhrUnFLanZWYldWUEd0SjNjcUdhdWdReHoxdXFxenRpUE5UdVNMT0o4QklINW1PRytISkNhTHltL2FrR3lkOTVYekwzQXp5VmxYVlRNRnkxOVhJdWRiUFpSby9rQyt6ODJROWduZXFqVlhWUHM1cEs3UloyMkRGVFQiLCJtYWMiOiJkZjNhMzdhZDFiY2U3YmIwYjU2ZmQxMDA0MzAwOTMxNTE2YWJkNGM0ZWY1ZjcxODgzNDQwNDJhZjlhNzQ4NjQ4IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357134195\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-433556339 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">49855</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/location/search/searchForSelect2?search=he</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/index.php/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"9 characters\">search=he</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IlRWbDJHZzBMMEZxb0t0amZNblRCdGc9PSIsInZhbHVlIjoiYWlhc0llMjd5aFIvV3lENWZwdE4rQzFmRmN0Z2k0L2dmV3JRWDlhbnBNOHBNVURTQmJCWmdudmdKM2pqenB2amVzNXdjOGl5dDRSVUF0RnNLWVhtTlBMRnRSTngvQVRmSFNNdGlDcjBwVGg2UFI1ZDBlVGJxaHpKeURldFgrUWkiLCJtYWMiOiIyZDc1NDBhYzNkNzRkZjFlYzE0NTcyZWU2ZWNhZDJjNzk1ZGU2ZjI0ZTlkN2Q1NDEyMThmMzZlYjkwNjViNmMwIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ink4K1I2cFpLMlJlbC9CbTBQZWdBcGc9PSIsInZhbHVlIjoiU2ZtMmdldUhrUnFLanZWYldWUEd0SjNjcUdhdWdReHoxdXFxenRpUE5UdVNMT0o4QklINW1PRytISkNhTHltL2FrR3lkOTVYekwzQXp5VmxYVlRNRnkxOVhJdWRiUFpSby9rQyt6ODJROWduZXFqVlhWUHM1cEs3UloyMkRGVFQiLCJtYWMiOiJkZjNhMzdhZDFiY2U3YmIwYjU2ZmQxMDA0MzAwOTMxNTE2YWJkNGM0ZWY1ZjcxODgzNDQwNDJhZjlhNzQ4NjQ4IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751825292.4488</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751825292</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433556339\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-476820830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476820830\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-684288779 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 18:08:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkczY05wSWtRVWpNT29OekJmRm0yT3c9PSIsInZhbHVlIjoiUUR0T2w0TXFnclVWQ3VjaUtwenFzMjQwa2M3ZDBma1N6cFBwdXdDallXZ0d0bVVEUHd0dlhWZlBhS1pacFZsbFkyRm5ZdkgyU3NFeGlKWEZvcXovOVlhbTRyT3NpSlpaWi90Nnk4Njczbk9rZjdQTy9kUzNVVXM2MmlXUjJ4RGkiLCJtYWMiOiI2ZTAwNmJjNjg1ZDhhM2NmNTEyNzA1OTcyN2U4YWE5NTIyZDg5MDE5NjJkYjg0MzVkYmVjNzQ4YmM3ZTg4N2UxIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:08:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IitJY3UzWGk5UmFZdEttYW1vd0w3bVE9PSIsInZhbHVlIjoicjNwRG9VdWZ6V3FBVThkdWpJRGdYT1FYK0tNbndyTGptbkFFSGppZVdnQU9mN3orb2dIZ3U1R0pUeVRtU0NHZk8zS3ZNQm5jaU5UcE44TkxxczBPbWh5ZkNKODFXZUxQS3NpYmxqMzdXN1NTZ2RlY0pmcGJnYVY0c1haMlo4clUiLCJtYWMiOiIzZDA4ZGNhNzIxZTMwMDMzMzM4MTYwNWRlODg4N2JkMzQ5NmVhOTZmNzM5MjUzNzRjY2FjZmMyYTE1NzdiMTk2IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:08:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkczY05wSWtRVWpNT29OekJmRm0yT3c9PSIsInZhbHVlIjoiUUR0T2w0TXFnclVWQ3VjaUtwenFzMjQwa2M3ZDBma1N6cFBwdXdDallXZ0d0bVVEUHd0dlhWZlBhS1pacFZsbFkyRm5ZdkgyU3NFeGlKWEZvcXovOVlhbTRyT3NpSlpaWi90Nnk4Njczbk9rZjdQTy9kUzNVVXM2MmlXUjJ4RGkiLCJtYWMiOiI2ZTAwNmJjNjg1ZDhhM2NmNTEyNzA1OTcyN2U4YWE5NTIyZDg5MDE5NjJkYjg0MzVkYmVjNzQ4YmM3ZTg4N2UxIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:08:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IitJY3UzWGk5UmFZdEttYW1vd0w3bVE9PSIsInZhbHVlIjoicjNwRG9VdWZ6V3FBVThkdWpJRGdYT1FYK0tNbndyTGptbkFFSGppZVdnQU9mN3orb2dIZ3U1R0pUeVRtU0NHZk8zS3ZNQm5jaU5UcE44TkxxczBPbWh5ZkNKODFXZUxQS3NpYmxqMzdXN1NTZ2RlY0pmcGJnYVY0c1haMlo4clUiLCJtYWMiOiIzZDA4ZGNhNzIxZTMwMDMzMzM4MTYwNWRlODg4N2JkMzQ5NmVhOTZmNzM5MjUzNzRjY2FjZmMyYTE1NzdiMTk2IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:08:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684288779\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-522275381 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-522275381\", {\"maxDepth\":0})</script>\n"}}