{"__meta": {"id": "Xf9d87261f28f49578b953732d71404d0", "datetime": "2025-07-06 16:04:01", "utime": 1751817841.28723, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[16:04:01] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751817841.279003, "collector": "log"}, {"message": "[16:04:01] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751817841.279017, "collector": "log"}, {"message": "[16:04:01] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751817841.279114, "collector": "log"}, {"message": "[16:04:01] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751817841.279127, "collector": "log"}]}, "time": {"start": 1751817841.257725, "end": 1751817841.287238, "duration": 0.029512882232666016, "duration_str": "29.51ms", "measures": [{"label": "Booting", "start": 1751817841.257725, "relative_start": 0, "end": 1751817841.278733, "relative_end": 1751817841.278733, "duration": 0.021008014678955078, "duration_str": "21.01ms", "params": [], "collector": null}, {"label": "Application", "start": 1751817841.278836, "relative_start": 0.021111011505126953, "end": 1751817841.287238, "relative_end": 0, "duration": 0.008401870727539062, "duration_str": "8.4ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3223640, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00481, "accumulated_duration_str": "4.81ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.0037, "duration_str": "3.7ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` = 'closed' and `updated_at` >= '2025-07-06 15:54:01' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "closed", "2025-07-06 15:54:01"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-235221958 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-235221958\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1155847003 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6IjZORE5BUWxBM3FRdVRDU2VEK2hkY0E9PSIsInZhbHVlIjoiUzlYRzZZZzB5dUZGYjZqaFJJMlBqQkNQQ09lR0tNdVEzZ1QrRnFjdktINmVQY2pwbUQyMWdmNVptWXNDQlA5OEZRZzNvNm9WOWtkWURZYjZDeDVINHI0ekFUdWpvSVR6K0NBbTNDMlNKdk5UampMVWZldERKcU56Sk5xZVNTbzYiLCJtYWMiOiI0ZmUzZGZjNTQ5YWE3ZGI0ZGI2MTg2ODhkMzQwOTEwMmEyMWRhYzYyODBmZjY3OGJkNzExOWQwZTVhOGZjNmJlIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkVDQjFyaU8zUm51WHpVOGo0c0FDRXc9PSIsInZhbHVlIjoieEZDbXd3SUhQU3o4djJoMjdHQU1qbFZwWVJnajF3QmVBZmxXcCtaNnVTc0pHUXNYVHZuRjBldngxRWtqb3J4eUxWZVppWjdvNGNPR2RWM3M4U0llcU8rMm41YlNMYytpOFRJb2w1LytzWUtxVGp3Zlk5NkRKS3lZcUJSTXdQZXgiLCJtYWMiOiJiZjJjMzliMDE1YWM1YzZmNWEwYWNiMWE2OTNkNDhkNTQ3MzUxZjUyZDU5YWFlOTVkZGExNzVlMTJkYzc5MTkyIiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IlEvWFJDMTh3R3dVaWNPdGFQQnB2d2c9PSIsInZhbHVlIjoiZ3ovTmxqK0ZTaUU1TGNHWE5taU9Pc0o2Nm1wSEIzRlVtdXhIVHBPREt3dzFQMXc2MVoyb3JKUHBFbUdEZ2lrOUZRK0JuY0MwQjR1YWxEOTFQdFg2MVA2a1Rqb2g4cWRPNGI2U0E3cE54TU54Z2JWMDM2czNzQmNCd1NUL2FLaU0iLCJtYWMiOiI1NDYyNThjNjFmZTRiNzY4ZjljNTI4NzQ1ZGFmY2E3OWFkNzhmNGY5MTc3YWZmOWM0OWYyN2VkN2M2ODlhYTdkIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155847003\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1370302228 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62564</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6IjZORE5BUWxBM3FRdVRDU2VEK2hkY0E9PSIsInZhbHVlIjoiUzlYRzZZZzB5dUZGYjZqaFJJMlBqQkNQQ09lR0tNdVEzZ1QrRnFjdktINmVQY2pwbUQyMWdmNVptWXNDQlA5OEZRZzNvNm9WOWtkWURZYjZDeDVINHI0ekFUdWpvSVR6K0NBbTNDMlNKdk5UampMVWZldERKcU56Sk5xZVNTbzYiLCJtYWMiOiI0ZmUzZGZjNTQ5YWE3ZGI0ZGI2MTg2ODhkMzQwOTEwMmEyMWRhYzYyODBmZjY3OGJkNzExOWQwZTVhOGZjNmJlIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkVDQjFyaU8zUm51WHpVOGo0c0FDRXc9PSIsInZhbHVlIjoieEZDbXd3SUhQU3o4djJoMjdHQU1qbFZwWVJnajF3QmVBZmxXcCtaNnVTc0pHUXNYVHZuRjBldngxRWtqb3J4eUxWZVppWjdvNGNPR2RWM3M4U0llcU8rMm41YlNMYytpOFRJb2w1LytzWUtxVGp3Zlk5NkRKS3lZcUJSTXdQZXgiLCJtYWMiOiJiZjJjMzliMDE1YWM1YzZmNWEwYWNiMWE2OTNkNDhkNTQ3MzUxZjUyZDU5YWFlOTVkZGExNzVlMTJkYzc5MTkyIiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IlEvWFJDMTh3R3dVaWNPdGFQQnB2d2c9PSIsInZhbHVlIjoiZ3ovTmxqK0ZTaUU1TGNHWE5taU9Pc0o2Nm1wSEIzRlVtdXhIVHBPREt3dzFQMXc2MVoyb3JKUHBFbUdEZ2lrOUZRK0JuY0MwQjR1YWxEOTFQdFg2MVA2a1Rqb2g4cWRPNGI2U0E3cE54TU54Z2JWMDM2czNzQmNCd1NUL2FLaU0iLCJtYWMiOiI1NDYyNThjNjFmZTRiNzY4ZjljNTI4NzQ1ZGFmY2E3OWFkNzhmNGY5MTc3YWZmOWM0OWYyN2VkN2M2ODlhYTdkIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751817841.2577</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751817841</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370302228\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1700596050 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>royaltransit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700596050\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1522158421 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 16:04:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImpUWUwrSnRYdWhiQ1ZtWVVmdXJkenc9PSIsInZhbHVlIjoiaVhNblR4ZTlvNWtDZmZEaFVaS3A0NWlpQ1poZ08wNElxd1BoODE3VllqS2JaRkQvTzZ3YWdnTnpWMXJpeXppWGNyTCt4b3RHV0NiVXpOQWVGOWs3akJZZkduTElzWitNWUc2Si9uSDlJWk1XVjRDaGRPWUpJRyt4aWt4U2NTNHEiLCJtYWMiOiJhNDJkZjk3ZGEwMDdiYzgzNDMyMTkwNGE5MGUxNWJjYzQ1MjIyYjk1M2ZjNzEyY2NjZmUwY2UzZjA2YmE3YTEzIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 18:04:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IndHcXpTOFk3YkpHbEtncndPTHNMZEE9PSIsInZhbHVlIjoiRXpmZTh0bUpxQWNQQUsybXJKK1U2blkwNUQ5eE5RcWFXUUFQN3ZyTGxOMHc3V1ZFSFBVTmsyWEJENXljc2RYdmFhanFSUEdBZFFGL1UvRzJRdHhLUlB5U3JhN3VLK0RRNHh2bFB4T05SODJwdEVlUnhtdHdQNWZJK0ZNZ1M2R3AiLCJtYWMiOiIxOWRiYWY1ZTBmMGU4YzBmMmNlZTA0OTdhODU4YWJiY2YxZDA4ZDc5ZWNlOTM2YjIxYzQ4MjNhYjExNzg1MjAwIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 18:04:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpUWUwrSnRYdWhiQ1ZtWVVmdXJkenc9PSIsInZhbHVlIjoiaVhNblR4ZTlvNWtDZmZEaFVaS3A0NWlpQ1poZ08wNElxd1BoODE3VllqS2JaRkQvTzZ3YWdnTnpWMXJpeXppWGNyTCt4b3RHV0NiVXpOQWVGOWs3akJZZkduTElzWitNWUc2Si9uSDlJWk1XVjRDaGRPWUpJRyt4aWt4U2NTNHEiLCJtYWMiOiJhNDJkZjk3ZGEwMDdiYzgzNDMyMTkwNGE5MGUxNWJjYzQ1MjIyYjk1M2ZjNzEyY2NjZmUwY2UzZjA2YmE3YTEzIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 18:04:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IndHcXpTOFk3YkpHbEtncndPTHNMZEE9PSIsInZhbHVlIjoiRXpmZTh0bUpxQWNQQUsybXJKK1U2blkwNUQ5eE5RcWFXUUFQN3ZyTGxOMHc3V1ZFSFBVTmsyWEJENXljc2RYdmFhanFSUEdBZFFGL1UvRzJRdHhLUlB5U3JhN3VLK0RRNHh2bFB4T05SODJwdEVlUnhtdHdQNWZJK0ZNZ1M2R3AiLCJtYWMiOiIxOWRiYWY1ZTBmMGU4YzBmMmNlZTA0OTdhODU4YWJiY2YxZDA4ZDc5ZWNlOTM2YjIxYzQ4MjNhYjExNzg1MjAwIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 18:04:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522158421\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2059829715 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059829715\", {\"maxDepth\":0})</script>\n"}}