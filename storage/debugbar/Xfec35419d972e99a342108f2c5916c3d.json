{"__meta": {"id": "Xfec35419d972e99a342108f2c5916c3d", "datetime": "2025-07-06 16:04:47", "utime": 1751817887.868671, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[16:04:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751817887.866563, "collector": "log"}, {"message": "[16:04:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751817887.866576, "collector": "log"}, {"message": "[16:04:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751817887.866673, "collector": "log"}, {"message": "[16:04:47] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751817887.866686, "collector": "log"}]}, "time": {"start": 1751817887.845676, "end": 1751817887.86868, "duration": 0.02300405502319336, "duration_str": "23ms", "measures": [{"label": "Booting", "start": 1751817887.845676, "relative_start": 0, "end": 1751817887.86629, "relative_end": 1751817887.86629, "duration": 0.020614147186279297, "duration_str": "20.61ms", "params": [], "collector": null}, {"label": "Application", "start": 1751817887.866407, "relative_start": 0.020730972290039062, "end": 1751817887.86868, "relative_end": 0, "duration": 0.002273082733154297, "duration_str": "2.27ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2731592, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-833774544 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-833774544\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-725842728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-725842728\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2106255203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2106255203\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2054430678 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6IjV4MjFUVUt3cXpoeW8xSjdYQ0JiOWc9PSIsInZhbHVlIjoiVml1Z1JnVjBSMG81MFVUcVU5Z2Y0TW9XamRKQkRhMGJOL2VlS0ZTOEFJK213b1R6WW9iYjZpYUhtWXhHY2hoeWRmUUZVbGNqa0UwU0lBdEI4ZGhNZG5LQzU5a0htMTJYNytWWjFtamxwTmJ4V3RrUzJhZ09PWWIyTmNPRll6UDUiLCJtYWMiOiI5ODAyNDU1ZTUyMWNlOTEwZTA3MzVhZWRjNDA3YzU1N2U4YzMyNWE1MzAyMzdjYTg5MDljZWJhOWJmYzlhNTBjIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6InI2NFd3TnB3NWVRSXJKYnNMRnpBUnc9PSIsInZhbHVlIjoiNlUwSkNkdlBWVktvSTlJTG16L0ZWdGhsejV6cDFjYlFrbllLQzZmMXArVzB4cVpIcjFwWTFEMHFpekd5cDhNWGE1UlJsc29LcGdwVGtqSldoNFpUVkVVeWVvcFpnS3VSR1NlUmhPTE1PWTlHRDNTcnNUVnRUOXFNN05ZN0U3NDUiLCJtYWMiOiJjYTY3NTM2Y2YwMTgxMmI3M2M4NWQ5ZGRlZGZiM2U2OWFlOGIyYzZlNmE3NDY3NmZmZGU3MzM5YjI4ZjU3YTdjIiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IlEvWFJDMTh3R3dVaWNPdGFQQnB2d2c9PSIsInZhbHVlIjoiZ3ovTmxqK0ZTaUU1TGNHWE5taU9Pc0o2Nm1wSEIzRlVtdXhIVHBPREt3dzFQMXc2MVoyb3JKUHBFbUdEZ2lrOUZRK0JuY0MwQjR1YWxEOTFQdFg2MVA2a1Rqb2g4cWRPNGI2U0E3cE54TU54Z2JWMDM2czNzQmNCd1NUL2FLaU0iLCJtYWMiOiI1NDYyNThjNjFmZTRiNzY4ZjljNTI4NzQ1ZGFmY2E3OWFkNzhmNGY5MTc3YWZmOWM0OWYyN2VkN2M2ODlhYTdkIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054430678\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1855902530 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62795</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2001 characters\">XSRF-TOKEN=eyJpdiI6IjV4MjFUVUt3cXpoeW8xSjdYQ0JiOWc9PSIsInZhbHVlIjoiVml1Z1JnVjBSMG81MFVUcVU5Z2Y0TW9XamRKQkRhMGJOL2VlS0ZTOEFJK213b1R6WW9iYjZpYUhtWXhHY2hoeWRmUUZVbGNqa0UwU0lBdEI4ZGhNZG5LQzU5a0htMTJYNytWWjFtamxwTmJ4V3RrUzJhZ09PWWIyTmNPRll6UDUiLCJtYWMiOiI5ODAyNDU1ZTUyMWNlOTEwZTA3MzVhZWRjNDA3YzU1N2U4YzMyNWE1MzAyMzdjYTg5MDljZWJhOWJmYzlhNTBjIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6InI2NFd3TnB3NWVRSXJKYnNMRnpBUnc9PSIsInZhbHVlIjoiNlUwSkNkdlBWVktvSTlJTG16L0ZWdGhsejV6cDFjYlFrbllLQzZmMXArVzB4cVpIcjFwWTFEMHFpekd5cDhNWGE1UlJsc29LcGdwVGtqSldoNFpUVkVVeWVvcFpnS3VSR1NlUmhPTE1PWTlHRDNTcnNUVnRUOXFNN05ZN0U3NDUiLCJtYWMiOiJjYTY3NTM2Y2YwMTgxMmI3M2M4NWQ5ZGRlZGZiM2U2OWFlOGIyYzZlNmE3NDY3NmZmZGU3MzM5YjI4ZjU3YTdjIiwidGFnIjoiIn0%3D; royaltransit_session=eyJpdiI6Imp1aVEvZEVKcXlsRjlPdEt6Wi84K3c9PSIsInZhbHVlIjoidUFEdTNYRmNzWkV6Z1pQNldhTmRrVkNZUTliRVdHZkVFWDJER2JDS3p4akhBSkNkZitJd3QwUnhmcnlnY2xZTUE1UFpHVGtoYlljTkhOSk4waEg0MTB6QlpTYmRPZG1uUzVreUZPVkRzUmo2bFNuTlE4bGtYOFhzU1NBMUFsY2wiLCJtYWMiOiIzOWRhN2I4OWE2ZGY5NTY5NTdmYzg2Zjc2OTI4ZTY2NDMxZWY1YThjZGRlODFmNzExZmEzNmRkNTAzNzEzMzkyIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IlEvWFJDMTh3R3dVaWNPdGFQQnB2d2c9PSIsInZhbHVlIjoiZ3ovTmxqK0ZTaUU1TGNHWE5taU9Pc0o2Nm1wSEIzRlVtdXhIVHBPREt3dzFQMXc2MVoyb3JKUHBFbUdEZ2lrOUZRK0JuY0MwQjR1YWxEOTFQdFg2MVA2a1Rqb2g4cWRPNGI2U0E3cE54TU54Z2JWMDM2czNzQmNCd1NUL2FLaU0iLCJtYWMiOiI1NDYyNThjNjFmZTRiNzY4ZjljNTI4NzQ1ZGFmY2E3OWFkNzhmNGY5MTc3YWZmOWM0OWYyN2VkN2M2ODlhYTdkIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751817887.8457</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751817887</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855902530\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-645220 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>royaltransit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-645220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1542958682 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 16:04:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNjb3Q2bEpZbVhZSWVIR0dtK0ttenc9PSIsInZhbHVlIjoiWldMTnpPeUFHQnJSOFZ6YlhOTmR5Nm5VcEpsUmRrRC8ra0JxVkdTdG0zckhIQVIxM1lNRm8wWXdmZDBJT3NDWDJndW9zbDd6RlVpKyt2bzlvSzVCZjNnZGRFTXRtclpUOXFrTjZTNGNUajh5TDYxNEpheVF0RlR1cWJUS2FYN3giLCJtYWMiOiJhYzJhOTYwMzRmNTdhNTY4YjcyMjAzZWY1NDcwMGE3MGNlZTUxZDEzMTVjNDNmZThkZGNkNDIxMGQ4NTFmZDJlIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 18:04:47 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IldTNmFHc3hCRTlSZFFwR2N1YXZ5R0E9PSIsInZhbHVlIjoiaVo4RUtRMnk5NS9QYUtMdTNZQ2JUM3RHMVRjYitFRGxRNWZlVklwNEYxZGNxc2NWNmJtVjFBOHgycE00UUIxUDN5NGNDQVVLdWtUTkV5UlpTeFFtOUJabytCOU5DR3pMYjh6RG1VK2gzSGJPNytXWGdOcFFVRGdGU2NkbzJhRk8iLCJtYWMiOiI3MzgxYjRiOWE5NmQ4N2Q3ZGVjZjJhOTllNzU0ZDAzNzlkMDQ5MDNhZTdkNzJhMTlmMWIxMTQxYTUzMmM0NGY1IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 18:04:47 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNjb3Q2bEpZbVhZSWVIR0dtK0ttenc9PSIsInZhbHVlIjoiWldMTnpPeUFHQnJSOFZ6YlhOTmR5Nm5VcEpsUmRrRC8ra0JxVkdTdG0zckhIQVIxM1lNRm8wWXdmZDBJT3NDWDJndW9zbDd6RlVpKyt2bzlvSzVCZjNnZGRFTXRtclpUOXFrTjZTNGNUajh5TDYxNEpheVF0RlR1cWJUS2FYN3giLCJtYWMiOiJhYzJhOTYwMzRmNTdhNTY4YjcyMjAzZWY1NDcwMGE3MGNlZTUxZDEzMTVjNDNmZThkZGNkNDIxMGQ4NTFmZDJlIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 18:04:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IldTNmFHc3hCRTlSZFFwR2N1YXZ5R0E9PSIsInZhbHVlIjoiaVo4RUtRMnk5NS9QYUtMdTNZQ2JUM3RHMVRjYitFRGxRNWZlVklwNEYxZGNxc2NWNmJtVjFBOHgycE00UUIxUDN5NGNDQVVLdWtUTkV5UlpTeFFtOUJabytCOU5DR3pMYjh6RG1VK2gzSGJPNytXWGdOcFFVRGdGU2NkbzJhRk8iLCJtYWMiOiI3MzgxYjRiOWE5NmQ4N2Q3ZGVjZjJhOTllNzU0ZDAzNzlkMDQ5MDNhZTdkNzJhMTlmMWIxMTQxYTUzMmM0NGY1IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 18:04:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1542958682\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1111390555 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1111390555\", {\"maxDepth\":0})</script>\n"}}