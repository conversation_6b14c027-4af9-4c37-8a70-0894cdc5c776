{"__meta": {"id": "X539d484ab3b53a0e47f3a5d52d1d1eec", "datetime": "2025-07-06 14:31:58", "utime": 1751812318.038698, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:31:58] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812318.036333, "collector": "log"}, {"message": "[14:31:58] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812318.036346, "collector": "log"}, {"message": "[14:31:58] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812318.036454, "collector": "log"}, {"message": "[14:31:58] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812318.036471, "collector": "log"}]}, "time": {"start": 1751812318.010647, "end": 1751812318.038708, "duration": 0.0280609130859375, "duration_str": "28.06ms", "measures": [{"label": "Booting", "start": 1751812318.010647, "relative_start": 0, "end": 1751812318.036042, "relative_end": 1751812318.036042, "duration": 0.025394916534423828, "duration_str": "25.39ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812318.036165, "relative_start": 0.025517940521240234, "end": 1751812318.038708, "relative_end": 0, "duration": 0.0025429725646972656, "duration_str": "2.54ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2725864, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-397792498 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-397792498\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2099182776 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2099182776\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1967507792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1967507792\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2046894670 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6InBXNk5RYmVNOEhabUora0dRTHV5cnc9PSIsInZhbHVlIjoiZWE4d3ltTURxYXBhTkJGMGhaYkpZSEtRY1FxMDg4cE1ZK2dhZk9XQXlKcEpxYUdxUVQvU2ZUaVV6SFN2QWlQcEVxN09odHRHNzIwZ2MwdzBtV0p1Yy9pR2hEV3cyYW9SZDlzdGNLbThPZ3ZCUDhraHR1dDdrQlpVVnpaWjhHR3kiLCJtYWMiOiI5MWMwMmIyYWYyZTUzZTEzYTVmNTRmOTU3NjNlYWM5ZDVlNGY1MWZiMzZlMTNlMTljMjZjNWEyNGFiNTdlYTYwIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjJ2M0VxWDFjMG9BUGE5RXg1cm5Db2c9PSIsInZhbHVlIjoiaFpRUGdNOEdMY2ltZnpTMFJnckxiTUNrN2EwMGplRXBHNmlubDQyY0RrSUVpeHFIVUVianFhaVpseCtCSUIrclhHc3VLL2ptZ1p5ZDhTM25DTzcrUjVuK3I3U1VmNVJvQ1d6eUNYbGtoaS9sd1FlRmdXaXA0VElzQTMwcERVRE0iLCJtYWMiOiI3YjNlNWQ2NmE3NzgwMjI4ZTM3NmZkOGU5NmJhYzc0MjliODJlNTZmOWMxY2NkYTg2MzBiMTJjNjEwMDQzMzk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046894670\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-348261731 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58692</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6InBXNk5RYmVNOEhabUora0dRTHV5cnc9PSIsInZhbHVlIjoiZWE4d3ltTURxYXBhTkJGMGhaYkpZSEtRY1FxMDg4cE1ZK2dhZk9XQXlKcEpxYUdxUVQvU2ZUaVV6SFN2QWlQcEVxN09odHRHNzIwZ2MwdzBtV0p1Yy9pR2hEV3cyYW9SZDlzdGNLbThPZ3ZCUDhraHR1dDdrQlpVVnpaWjhHR3kiLCJtYWMiOiI5MWMwMmIyYWYyZTUzZTEzYTVmNTRmOTU3NjNlYWM5ZDVlNGY1MWZiMzZlMTNlMTljMjZjNWEyNGFiNTdlYTYwIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjJ2M0VxWDFjMG9BUGE5RXg1cm5Db2c9PSIsInZhbHVlIjoiaFpRUGdNOEdMY2ltZnpTMFJnckxiTUNrN2EwMGplRXBHNmlubDQyY0RrSUVpeHFIVUVianFhaVpseCtCSUIrclhHc3VLL2ptZ1p5ZDhTM25DTzcrUjVuK3I3U1VmNVJvQ1d6eUNYbGtoaS9sd1FlRmdXaXA0VElzQTMwcERVRE0iLCJtYWMiOiI3YjNlNWQ2NmE3NzgwMjI4ZTM3NmZkOGU5NmJhYzc0MjliODJlNTZmOWMxY2NkYTg2MzBiMTJjNjEwMDQzMzk3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812318.0106</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812318</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348261731\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1686995571 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686995571\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-587887584 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:31:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImhQRmNrdkN0M2lUdWt5SE94Mm1RL0E9PSIsInZhbHVlIjoiSzE0ZWhlL3h2M2NldDZqRERTak9TYyt2aTI2Nkg0SVhrc0VCc2JtNjU0bUFqRFRKZE5ZR3BkVVBNWC8vV0pQOVJkdVNZNzY2c0JKSVlhb21FbG1pTkQ4b0hOYTVXbCtGT2FlMVhhYU5sMXFaaW84YjFYc2xoZnZGV1ZXZHZaRjUiLCJtYWMiOiIzNDg4ZTc2MTI4YmMzZjJkMjNlNzAyOTc5ZGQ0OWI5MzIyNTMyMTlmMDIyZjY2OWQ0ZGU1NGE2MDI2OGRhYzNlIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:58 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IlNRRWtDU1RUUXhNM2kzVkRlak54WkE9PSIsInZhbHVlIjoiYS9maE1vWm0xOHZrRDFhV2QzbDd6bmVneWhHSzQ2ck1ZaXorb0lrcmhKMnY0ajRCS2dtRERjamtabDdyS0RsdVZ0K1dCVXdZNHdDUmlOZWx0SW1sMUlPUFhxdk5mR2VWbjhRNzRMSURoOCs4bFJBbmFuU1hSalpSRGlFZDdFcHEiLCJtYWMiOiIxYzFmNmRlMWEyMWQxYzBmYzAzODg2YmJlNjA5NjE2ZDg0ODEyMzg5MzU1NTA1ZGZmNDNkMjczZjg0ODUyYjgwIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:58 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImhQRmNrdkN0M2lUdWt5SE94Mm1RL0E9PSIsInZhbHVlIjoiSzE0ZWhlL3h2M2NldDZqRERTak9TYyt2aTI2Nkg0SVhrc0VCc2JtNjU0bUFqRFRKZE5ZR3BkVVBNWC8vV0pQOVJkdVNZNzY2c0JKSVlhb21FbG1pTkQ4b0hOYTVXbCtGT2FlMVhhYU5sMXFaaW84YjFYc2xoZnZGV1ZXZHZaRjUiLCJtYWMiOiIzNDg4ZTc2MTI4YmMzZjJkMjNlNzAyOTc5ZGQ0OWI5MzIyNTMyMTlmMDIyZjY2OWQ0ZGU1NGE2MDI2OGRhYzNlIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IlNRRWtDU1RUUXhNM2kzVkRlak54WkE9PSIsInZhbHVlIjoiYS9maE1vWm0xOHZrRDFhV2QzbDd6bmVneWhHSzQ2ck1ZaXorb0lrcmhKMnY0ajRCS2dtRERjamtabDdyS0RsdVZ0K1dCVXdZNHdDUmlOZWx0SW1sMUlPUFhxdk5mR2VWbjhRNzRMSURoOCs4bFJBbmFuU1hSalpSRGlFZDdFcHEiLCJtYWMiOiIxYzFmNmRlMWEyMWQxYzBmYzAzODg2YmJlNjA5NjE2ZDg0ODEyMzg5MzU1NTA1ZGZmNDNkMjczZjg0ODUyYjgwIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587887584\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-458292683 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-458292683\", {\"maxDepth\":0})</script>\n"}}