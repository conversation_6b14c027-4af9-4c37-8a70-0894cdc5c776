{"__meta": {"id": "Xce50a070596a9ffa17efabda105127c4", "datetime": "2025-07-06 14:34:04", "utime": 1751812444.641177, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:34:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812444.632813, "collector": "log"}, {"message": "[14:34:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812444.632826, "collector": "log"}, {"message": "[14:34:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812444.632936, "collector": "log"}, {"message": "[14:34:04] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812444.632953, "collector": "log"}]}, "time": {"start": 1751812444.607797, "end": 1751812444.641187, "duration": 0.033390045166015625, "duration_str": "33.39ms", "measures": [{"label": "Booting", "start": 1751812444.607797, "relative_start": 0, "end": 1751812444.63246, "relative_end": 1751812444.63246, "duration": 0.024663209915161133, "duration_str": "24.66ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812444.632601, "relative_start": 0.024804115295410156, "end": 1751812444.641188, "relative_end": 9.5367431640625e-07, "duration": 0.008586883544921875, "duration_str": "8.59ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3217912, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0046099999999999995, "accumulated_duration_str": "4.61ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.0033599999999999997, "duration_str": "3.36ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` = 'closed' and `updated_at` >= '2025-07-06 14:24:04' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "closed", "2025-07-06 14:24:04"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1754685638 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1754685638\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-925598248 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6InR1WS91b1JUWmpNTDc5WTJZemRsMWc9PSIsInZhbHVlIjoick56bUZNbW9yWCtoY1JveGVTbnlHMGR6bm9remJjVlluZUJtSUxoOS9qNHJueGxkMjhDZW1rVnRkUmZNNkl4bWpGZ2VubU9GN0toTk4xdStaVHpqTFVSSE42NU96YW01TlBDWU5JNmN0M25hRGdzYjVWWUUzZU9zdHVqWk9HRXEiLCJtYWMiOiJmYWM3YzNmNzUxNmM0NDAxNmQ2YjhhNzhjMWJjYTNiYTllZDY1ZTYzY2M1MDgxNjFjNjE5OWY1ZThiODRiZmY3IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlF0YjJRaUpZaXNUeTNKK2M2WFZWNEE9PSIsInZhbHVlIjoiTVloRXluNURLZDJja0RpUlZDS1JmTmt0QTdteE1aSEZVZVlzODVCa0tiZWhmbGJ2TzBicGFxMzY0eU13U0V1eFF2QmVUVmZ6RTZ2MEFVK292RjEyMVdYcWZEMVFGRlBMTFVRNy9OK2F4KzYzZkE5UnJCdFFjR09SN1FWeWREMG0iLCJtYWMiOiIzMmNlNTNhYTVhMDBjNmI3Y2NjYTVjZmNmYmY1N2Q0OGU5ODQ0YjAzMjQzOGFhZjBjNjk2YTZhYWZmZDYzZmM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925598248\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1101278159 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59197</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6InR1WS91b1JUWmpNTDc5WTJZemRsMWc9PSIsInZhbHVlIjoick56bUZNbW9yWCtoY1JveGVTbnlHMGR6bm9remJjVlluZUJtSUxoOS9qNHJueGxkMjhDZW1rVnRkUmZNNkl4bWpGZ2VubU9GN0toTk4xdStaVHpqTFVSSE42NU96YW01TlBDWU5JNmN0M25hRGdzYjVWWUUzZU9zdHVqWk9HRXEiLCJtYWMiOiJmYWM3YzNmNzUxNmM0NDAxNmQ2YjhhNzhjMWJjYTNiYTllZDY1ZTYzY2M1MDgxNjFjNjE5OWY1ZThiODRiZmY3IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IlF0YjJRaUpZaXNUeTNKK2M2WFZWNEE9PSIsInZhbHVlIjoiTVloRXluNURLZDJja0RpUlZDS1JmTmt0QTdteE1aSEZVZVlzODVCa0tiZWhmbGJ2TzBicGFxMzY0eU13U0V1eFF2QmVUVmZ6RTZ2MEFVK292RjEyMVdYcWZEMVFGRlBMTFVRNy9OK2F4KzYzZkE5UnJCdFFjR09SN1FWeWREMG0iLCJtYWMiOiIzMmNlNTNhYTVhMDBjNmI3Y2NjYTVjZmNmYmY1N2Q0OGU5ODQ0YjAzMjQzOGFhZjBjNjk2YTZhYWZmZDYzZmM5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812444.6078</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812444</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101278159\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1307769526 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307769526\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-208972384 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:34:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJtUlJlK1RqNFFNeUJmSkJid1dIOHc9PSIsInZhbHVlIjoiWUpKSTlTYjJkUFFwcDJabkR5ZGtuZW9nR0VKYXZVckxCVmxIRGQvV29paW1ieGxvM1puam9HSjc4Wm04b0dpZzNPNVlVYmxVaGNiOWRSQUNtV0k1MENNVmlYZS90REYwa3FiT1JmdkJKdVJJWk1Oa0twMGhMSzdkUDcyeUY1M1MiLCJtYWMiOiJjN2QzOTQzY2Q3Y2Y5NjgzY2NlYTA1N2RjNWM2Mzk2YmRhMmZjMmMyMzg3NTY4MjgxMTMwZjRjNjJhZjQyNzVkIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:34:04 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6Ik8zTkNrdGhZS3o2SkdYeFJoMmhyUXc9PSIsInZhbHVlIjoiVFZrdm5UM1RmNXBZaDE5UitNaytKM2VCeGl2Z1lWSWt2SEJjWTUvaTN5dDBveUhDUnFpKzZvSCtLbTBIMDJZR1FsRCtuOXpNaXhCMXlkbFFuanhtcUtoUWYwTG5zL1pYOEtMSTM5V0tOUW96Qkp2YVpFZXgrdDhlSTRWM01tbGciLCJtYWMiOiJiNjJhMWNlZTI1ODM3MmFmZWNhNmRhYmFhYWU5MzQ3YWY4OGE5YWU1M2VlNjA4MjIxZjE3NzczYzA3OTdkZmRiIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:34:04 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJtUlJlK1RqNFFNeUJmSkJid1dIOHc9PSIsInZhbHVlIjoiWUpKSTlTYjJkUFFwcDJabkR5ZGtuZW9nR0VKYXZVckxCVmxIRGQvV29paW1ieGxvM1puam9HSjc4Wm04b0dpZzNPNVlVYmxVaGNiOWRSQUNtV0k1MENNVmlYZS90REYwa3FiT1JmdkJKdVJJWk1Oa0twMGhMSzdkUDcyeUY1M1MiLCJtYWMiOiJjN2QzOTQzY2Q3Y2Y5NjgzY2NlYTA1N2RjNWM2Mzk2YmRhMmZjMmMyMzg3NTY4MjgxMTMwZjRjNjJhZjQyNzVkIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:34:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6Ik8zTkNrdGhZS3o2SkdYeFJoMmhyUXc9PSIsInZhbHVlIjoiVFZrdm5UM1RmNXBZaDE5UitNaytKM2VCeGl2Z1lWSWt2SEJjWTUvaTN5dDBveUhDUnFpKzZvSCtLbTBIMDJZR1FsRCtuOXpNaXhCMXlkbFFuanhtcUtoUWYwTG5zL1pYOEtMSTM5V0tOUW96Qkp2YVpFZXgrdDhlSTRWM01tbGciLCJtYWMiOiJiNjJhMWNlZTI1ODM3MmFmZWNhNmRhYmFhYWU5MzQ3YWY4OGE5YWU1M2VlNjA4MjIxZjE3NzczYzA3OTdkZmRiIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:34:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208972384\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-847201128 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847201128\", {\"maxDepth\":0})</script>\n"}}