{"__meta": {"id": "X8f182939c5efd3db0cd1192bf00fd164", "datetime": "2025-07-06 18:08:32", "utime": 1751825312.188303, "method": "GET", "uri": "/location/search/searchForSelect2?search=cai", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[18:08:32] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825312.178896, "collector": "log"}, {"message": "[18:08:32] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825312.17891, "collector": "log"}, {"message": "[18:08:32] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751825312.179024, "collector": "log"}, {"message": "[18:08:32] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751825312.179038, "collector": "log"}]}, "time": {"start": 1751825312.144341, "end": 1751825312.188312, "duration": 0.04397106170654297, "duration_str": "43.97ms", "measures": [{"label": "Booting", "start": 1751825312.144341, "relative_start": 0, "end": 1751825312.178536, "relative_end": 1751825312.178536, "duration": 0.0341949462890625, "duration_str": "34.19ms", "params": [], "collector": null}, {"label": "Application", "start": 1751825312.178681, "relative_start": 0.03433990478515625, "end": 1751825312.188313, "relative_end": 9.5367431640625e-07, "duration": 0.009632110595703125, "duration_str": "9.63ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3308600, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET location/search/searchForSelect2", "middleware": "web", "controller": "Modules\\Location\\Controllers\\LocationController@searchForSelect2", "namespace": "Modules\\Location\\Controllers", "prefix": "/location", "where": [], "as": "location.searchForSelect", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Location/Controllers/LocationController.php&line=44\">modules/Location/Controllers/LocationController.php:44-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.004900000000000001, "accumulated_duration_str": "4.9ms", "statements": [{"sql": "select `airports`.*, `airports`.`name` as `title` from `airports` where `airports`.`name` like '%cai%' or `airports`.`code` like '%cai%' order by `name` asc limit 20", "type": "query", "params": [], "bindings": ["%cai%", "%cai%"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/modules/Location/Controllers/LocationController.php", "line": 61}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "duration": 0.004900000000000001, "duration_str": "4.9ms", "stmt_id": "/modules/Location/Controllers/LocationController.php:61", "connection": "megafly"}]}, "models": {"data": {"App\\Models\\Airports": 1}, "count": 1}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/location/search/searchForSelect2", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cai</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1894568079 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1894568079\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1108383351 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImM5TzVSa2Z0d3FSK25NWU5QMFE3aXc9PSIsInZhbHVlIjoiWjRKN0JGNlN4ejNRWkdvdDQyZDlBakpRVFkwZ2xBTUd2MThDNk1pYjA0cllldHRUTHFVU2VkanVYL2NYNVplMW5FVHhYSit0WnhPUVNBVURYOXJXRXNBQ0JpVTRwZnpXRjE2cUIyL2V5bUY2UldBSUFud2JqcFpaTzhrZ2JjS2MiLCJtYWMiOiI4YWVkNzhmNzUxZGRlYjgxYmYzM2FiN2I2ZjFmMDhhOTBhNzYxZTc3MzI0MDliNjQ4ZjBmYjU5Yjk3MmQ5YzVmIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjhpSnlqSXRQUHBUYm02b1huUzl3cVE9PSIsInZhbHVlIjoiUXh1bzNZRVUvWUZ6clgzNmpHNWlTVnlHTWt3QjVZeE9waEFLU3RWT21tSEt5VlRzSlBBOEkxdFdTNE42Q09HZnA3eTBBMGw0RXRYR3Vqcm5JWkIyWVJmUEJpNE9vU0hXVEhXU2NYL0hDcCs0ZncyUVY4aVRXZHQ0c25hWFI5RGsiLCJtYWMiOiJkM2Q1YWQ1ZTdlMDgwMjQxM2YxNmMyZTQ0Mzc4MTg3YjMzYjk3ZjBkOTE5YzIzMDYwODRhNjJkMWI3NjA4ZjZhIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108383351\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1077145447 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">49980</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/location/search/searchForSelect2?search=cai</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/index.php/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">search=cai</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImM5TzVSa2Z0d3FSK25NWU5QMFE3aXc9PSIsInZhbHVlIjoiWjRKN0JGNlN4ejNRWkdvdDQyZDlBakpRVFkwZ2xBTUd2MThDNk1pYjA0cllldHRUTHFVU2VkanVYL2NYNVplMW5FVHhYSit0WnhPUVNBVURYOXJXRXNBQ0JpVTRwZnpXRjE2cUIyL2V5bUY2UldBSUFud2JqcFpaTzhrZ2JjS2MiLCJtYWMiOiI4YWVkNzhmNzUxZGRlYjgxYmYzM2FiN2I2ZjFmMDhhOTBhNzYxZTc3MzI0MDliNjQ4ZjBmYjU5Yjk3MmQ5YzVmIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IjhpSnlqSXRQUHBUYm02b1huUzl3cVE9PSIsInZhbHVlIjoiUXh1bzNZRVUvWUZ6clgzNmpHNWlTVnlHTWt3QjVZeE9waEFLU3RWT21tSEt5VlRzSlBBOEkxdFdTNE42Q09HZnA3eTBBMGw0RXRYR3Vqcm5JWkIyWVJmUEJpNE9vU0hXVEhXU2NYL0hDcCs0ZncyUVY4aVRXZHQ0c25hWFI5RGsiLCJtYWMiOiJkM2Q1YWQ1ZTdlMDgwMjQxM2YxNmMyZTQ0Mzc4MTg3YjMzYjk3ZjBkOTE5YzIzMDYwODRhNjJkMWI3NjA4ZjZhIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjFmckd6UWNsMFJKNmdzdzM3a254aHc9PSIsInZhbHVlIjoiWnZwbE44enhqZ25jeHZ5dzF6cjN4T2NyRkRLdVZadHRaZzlmTVFlMUx3c29QMWdEbXkwODNJa3ZCZ3huU3BwelJ3ZmlKNUk5bXUzUy95T25wdno1WXZEWUJ2R29GdnlIR2RVUmtMNlNBUnQrZG01TjZHRk80RFhCeExYMGRWOWwiLCJtYWMiOiJjNGJkYzU5M2QyM2VkOTRjZDU5ZjMyNWQzYmUwMWFmMzg4ZDcyMjk4OGZjY2EwMjA2MjJhZTkwZGM0NjA3Yzc1IiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751825312.1443</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751825312</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077145447\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-276750810 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-276750810\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-369948489 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 18:08:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkYxOGhYNkllTVpTQUhvR1lya0twb3c9PSIsInZhbHVlIjoieFJUNVlqdG9pdkt3YzVaRWlyTzIrdlYyKzd1UFNqaC9DbGJUSktxajB2azdaRE0rL1ByY01tQ3M3R3BBVUZQSDcvYjcwc1h2MXVhd0NiZXFpMVBtRGJZbmRmVERtQUR0K0ZYcDZ4SW1OUU03L1pmRS9raWNIYUt3ekhKMGZaQXAiLCJtYWMiOiIyNGIxOGU0MjY0Zjk5ZGU2ZjJkMTQ4MTY5ZWYyZDAxNGI3MmJjMjM3Mjk0MjExZTExMjBiYTNhZWM2M2MwNzQzIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:08:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6Ik9ocjFsSW5oVlhJNSsxY2QraHBUM2c9PSIsInZhbHVlIjoieXNESzdoZ0NNcWxsdkNLL0lCTVhMTVVqblJpbFlkS3g0dUdOd1lDKytWWkZwVncwcUdteFVPcFhGT1ljRXFhd2xwbDhFazBpdnBKbXJzWXdEMCtKeUF4ZDZRckZJay94aGNIOHowdlVZQkd0R1BoOXlyeWdLVVZpT1dkb1ZFTVMiLCJtYWMiOiI2Mzk3ODc1OWRmYzQxNWUzNzQ1Y2NlOWE4ZDU5MWQ5MWUwMTg5NGViNjU5MTMyMGNkMTIwOWVmMDQ0ZDhhYWFjIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 20:08:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkYxOGhYNkllTVpTQUhvR1lya0twb3c9PSIsInZhbHVlIjoieFJUNVlqdG9pdkt3YzVaRWlyTzIrdlYyKzd1UFNqaC9DbGJUSktxajB2azdaRE0rL1ByY01tQ3M3R3BBVUZQSDcvYjcwc1h2MXVhd0NiZXFpMVBtRGJZbmRmVERtQUR0K0ZYcDZ4SW1OUU03L1pmRS9raWNIYUt3ekhKMGZaQXAiLCJtYWMiOiIyNGIxOGU0MjY0Zjk5ZGU2ZjJkMTQ4MTY5ZWYyZDAxNGI3MmJjMjM3Mjk0MjExZTExMjBiYTNhZWM2M2MwNzQzIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:08:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6Ik9ocjFsSW5oVlhJNSsxY2QraHBUM2c9PSIsInZhbHVlIjoieXNESzdoZ0NNcWxsdkNLL0lCTVhMTVVqblJpbFlkS3g0dUdOd1lDKytWWkZwVncwcUdteFVPcFhGT1ljRXFhd2xwbDhFazBpdnBKbXJzWXdEMCtKeUF4ZDZRckZJay94aGNIOHowdlVZQkd0R1BoOXlyeWdLVVZpT1dkb1ZFTVMiLCJtYWMiOiI2Mzk3ODc1OWRmYzQxNWUzNzQ1Y2NlOWE4ZDU5MWQ5MWUwMTg5NGViNjU5MTMyMGNkMTIwOWVmMDQ0ZDhhYWFjIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 20:08:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369948489\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1695822926 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695822926\", {\"maxDepth\":0})</script>\n"}}