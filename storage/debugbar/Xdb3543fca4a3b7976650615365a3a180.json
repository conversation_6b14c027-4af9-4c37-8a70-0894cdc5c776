{"__meta": {"id": "Xdb3543fca4a3b7976650615365a3a180", "datetime": "2025-07-06 15:23:27", "utime": 1751815407.710444, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[15:23:27] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815407.703234, "collector": "log"}, {"message": "[15:23:27] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815407.703247, "collector": "log"}, {"message": "[15:23:27] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815407.703366, "collector": "log"}, {"message": "[15:23:27] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815407.703378, "collector": "log"}]}, "time": {"start": 1751815407.670634, "end": 1751815407.710451, "duration": 0.039816856384277344, "duration_str": "39.82ms", "measures": [{"label": "Booting", "start": 1751815407.670634, "relative_start": 0, "end": 1751815407.70293, "relative_end": 1751815407.70293, "duration": 0.032295942306518555, "duration_str": "32.3ms", "params": [], "collector": null}, {"label": "Application", "start": 1751815407.703044, "relative_start": 0.0324099063873291, "end": 1751815407.710451, "relative_end": 0, "duration": 0.007406949996948242, "duration_str": "7.41ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3223520, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00408, "accumulated_duration_str": "4.08ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00302, "duration_str": "3.02ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q' and `status` = 'closed' and `updated_at` >= '2025-07-06 15:13:27' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q", "closed", "2025-07-06 15:13:27"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1995235990 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1995235990\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-349160464 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImpmSHh3NWZYNVhXV1ljK0pueWFTbUE9PSIsInZhbHVlIjoiV1FTR1lmWmRmbjFPb0tCVk15RitBQ3M1cyswZHNtblY0aXoxdk5COGNzaWFvaWhrcUwreDdtU3kxaUx3Tmp4alpZVmc0cVRHNkVPMlZxTjd3dHdJdjAvNTdDUkdDQTNtUmkzakNVazg2TktxZFZmaU52amxWaEEvOWYzUWxvM1AiLCJtYWMiOiIxNGFlMGRiYzFhMWRhYjNhNzY1MjE0NjAwY2M1N2E3MDgxYzIyMTllY2Y2ZGY2ZTY2MTBiYjNhYzhlMmRlMmEyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Im9QOFRUWlFtekI0Q0VWYlRNdDV0eUE9PSIsInZhbHVlIjoiMTVmMU9nU0hYZndZM2p0dEZhVWNlOTU2d1B2eTNnYWdSUFFEOGxtOUFwcWszMmhnaEtpQzJ1RW5kem5LdWVZNExFUWFxamljRUJWeGxrbTY1Q1h4dFIzMGV1SVpIcGxqNzZMVlJMNVNSSzNJeVl5K29PUHJrYjFpQSsrcEcxRW0iLCJtYWMiOiJjMDkwMzY3YTY4MzExOWYyMTQ4OGMzZmIxMTA0OGVmMTRiODlmNmQyMzlmZmIwNjlhNTM5NDRmMGUyY2Q5ZWUzIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6InN0Y2hPS21kYVE1Z21VbjFYWWQ3Vmc9PSIsInZhbHVlIjoiUEIrVzhxQks5Yml3VWx2MDRVcmdtTDZJWXFQU1ZHbjhuNXRKUHE2SHdTV1A5cUVYU2hUZFBnT0NRaG80NEpQVStMT1EzdDJBRGdlY1dqVUYyZytVRFV3cU5Ncm9FQ3lWNTllOUt2M0pwRGFNdVJVRUwyaXJIdE5rQVk0aWxUak8iLCJtYWMiOiI1ZmZiYTE2NDZlMWI5NjZmY2ZkYWM5N2UxNWZhMjM0MTZjNWRkOGVhY2IxNzZhNTc4ZTkyODYzZWI0NGJkZmJiIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349160464\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1968474075 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55422</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6ImpmSHh3NWZYNVhXV1ljK0pueWFTbUE9PSIsInZhbHVlIjoiV1FTR1lmWmRmbjFPb0tCVk15RitBQ3M1cyswZHNtblY0aXoxdk5COGNzaWFvaWhrcUwreDdtU3kxaUx3Tmp4alpZVmc0cVRHNkVPMlZxTjd3dHdJdjAvNTdDUkdDQTNtUmkzakNVazg2TktxZFZmaU52amxWaEEvOWYzUWxvM1AiLCJtYWMiOiIxNGFlMGRiYzFhMWRhYjNhNzY1MjE0NjAwY2M1N2E3MDgxYzIyMTllY2Y2ZGY2ZTY2MTBiYjNhYzhlMmRlMmEyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Im9QOFRUWlFtekI0Q0VWYlRNdDV0eUE9PSIsInZhbHVlIjoiMTVmMU9nU0hYZndZM2p0dEZhVWNlOTU2d1B2eTNnYWdSUFFEOGxtOUFwcWszMmhnaEtpQzJ1RW5kem5LdWVZNExFUWFxamljRUJWeGxrbTY1Q1h4dFIzMGV1SVpIcGxqNzZMVlJMNVNSSzNJeVl5K29PUHJrYjFpQSsrcEcxRW0iLCJtYWMiOiJjMDkwMzY3YTY4MzExOWYyMTQ4OGMzZmIxMTA0OGVmMTRiODlmNmQyMzlmZmIwNjlhNTM5NDRmMGUyY2Q5ZWUzIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6InN0Y2hPS21kYVE1Z21VbjFYWWQ3Vmc9PSIsInZhbHVlIjoiUEIrVzhxQks5Yml3VWx2MDRVcmdtTDZJWXFQU1ZHbjhuNXRKUHE2SHdTV1A5cUVYU2hUZFBnT0NRaG80NEpQVStMT1EzdDJBRGdlY1dqVUYyZytVRFV3cU5Ncm9FQ3lWNTllOUt2M0pwRGFNdVJVRUwyaXJIdE5rQVk0aWxUak8iLCJtYWMiOiI1ZmZiYTE2NDZlMWI5NjZmY2ZkYWM5N2UxNWZhMjM0MTZjNWRkOGVhY2IxNzZhNTc4ZTkyODYzZWI0NGJkZmJiIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751815407.6706</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751815407</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968474075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1688695658 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1688695658\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-916296551 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 15:23:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im0wSzZyaTlHM2gvMFdUN2NSTm05bEE9PSIsInZhbHVlIjoiK04ra25lVnhkTXpVNzNsanQwSnc5dXBiVEsyekhESUVXS3k4eGJPZG0vYVd3aXBiNGNnQXEwM2F6UHUvalMxUkpMbXRlY0tQRHlXWW5wZE1xNWFrKzNRTis4SGllNWZ0eUpaYWJLK1ovdmJUQXpHTUxrQWM1a3VBOVcvN3piMGciLCJtYWMiOiIzOGJiZjkwMGNiMDcxODc3YTNlMTAyMzU2NWY2MGIyM2VhYTI0MGQ0ZjczNDVlOTgwOGY1NjYwZjI3MjVmNTljIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:23:27 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6ImVIYW1uSkI0T3ZaVzN5WGREUVo1Wnc9PSIsInZhbHVlIjoieGg5UHpTOWR6L0hienAyeEluZHozOG91eE9QUEJGNjJrT1NUT0FzVFJaM2VJdnhXT1J1WTVZMng5YmZTanpPbHZ0c0tzbzdMRDgvNGY0THJhWm9DZkRtUmhZUGt5NDk5MUtwc2NSNTUxMVpGZmFkWU1WY1hid0J6dFEyNGpScVIiLCJtYWMiOiIwNTA1ZmMyMzRiOTFhZGE1M2UzYjQwNWY4N2NhMDk5ZTA1YmYyY2M2NGY5NGNiYzNkZDc3ZDVhYmQ1MDc5MjFjIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:23:27 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im0wSzZyaTlHM2gvMFdUN2NSTm05bEE9PSIsInZhbHVlIjoiK04ra25lVnhkTXpVNzNsanQwSnc5dXBiVEsyekhESUVXS3k4eGJPZG0vYVd3aXBiNGNnQXEwM2F6UHUvalMxUkpMbXRlY0tQRHlXWW5wZE1xNWFrKzNRTis4SGllNWZ0eUpaYWJLK1ovdmJUQXpHTUxrQWM1a3VBOVcvN3piMGciLCJtYWMiOiIzOGJiZjkwMGNiMDcxODc3YTNlMTAyMzU2NWY2MGIyM2VhYTI0MGQ0ZjczNDVlOTgwOGY1NjYwZjI3MjVmNTljIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:23:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6ImVIYW1uSkI0T3ZaVzN5WGREUVo1Wnc9PSIsInZhbHVlIjoieGg5UHpTOWR6L0hienAyeEluZHozOG91eE9QUEJGNjJrT1NUT0FzVFJaM2VJdnhXT1J1WTVZMng5YmZTanpPbHZ0c0tzbzdMRDgvNGY0THJhWm9DZkRtUmhZUGt5NDk5MUtwc2NSNTUxMVpGZmFkWU1WY1hid0J6dFEyNGpScVIiLCJtYWMiOiIwNTA1ZmMyMzRiOTFhZGE1M2UzYjQwNWY4N2NhMDk5ZTA1YmYyY2M2NGY5NGNiYzNkZDc3ZDVhYmQ1MDc5MjFjIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:23:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-916296551\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-311082318 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311082318\", {\"maxDepth\":0})</script>\n"}}