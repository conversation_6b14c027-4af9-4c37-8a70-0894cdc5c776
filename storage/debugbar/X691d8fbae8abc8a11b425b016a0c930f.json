{"__meta": {"id": "X691d8fbae8abc8a11b425b016a0c930f", "datetime": "2025-07-06 14:33:14", "utime": 1751812394.477948, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:33:14] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812394.475827, "collector": "log"}, {"message": "[14:33:14] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812394.47584, "collector": "log"}, {"message": "[14:33:14] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812394.475942, "collector": "log"}, {"message": "[14:33:14] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812394.475956, "collector": "log"}]}, "time": {"start": 1751812394.450466, "end": 1751812394.477956, "duration": 0.02749013900756836, "duration_str": "27.49ms", "measures": [{"label": "Booting", "start": 1751812394.450466, "relative_start": 0, "end": 1751812394.475536, "relative_end": 1751812394.475536, "duration": 0.0250701904296875, "duration_str": "25.07ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812394.475657, "relative_start": 0.025191068649291992, "end": 1751812394.477956, "relative_end": 0, "duration": 0.002299070358276367, "duration_str": "2.3ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2725864, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-652896424 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-652896424\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1926788190 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1926788190\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1545894193 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1545894193\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-539039967 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6InNJa0FtYXJsaW9ET0Q3eG9Odmk1Qnc9PSIsInZhbHVlIjoibjM2T0c3QlIzVlpVbEhOeldmcmkrUTBPUlFBNHNqaHByRno5WEZYcGphMlBKUVUvclAzUkcvOWF6dlN5bFFIR2lEZzJWUC9taVFqNUFLNUNiMUZFMXFUYVZITHgyVUloZkhhTDFQeExoTk12eVlxK0VXcEdHZkZ5cDZ4YjdWR2MiLCJtYWMiOiJiYmM4MTBiYWI5MmE0Yzc1NmNmNWE4NWYyMmEwZjVlMmNkZjBlNGFjZGRhOTk5NzQ0ZGRmMmVjZjVmM2ZkYTgxIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkVvMnRCaTlOeitNRnF4VFh2RjU3TGc9PSIsInZhbHVlIjoibjEzV00vN21iREpQTDRsVkIzMVZQUTVlS1VZbWFPbFhrMHVZL1JlVGRXT1h6VUVaakdYSmhnVXVSQll3MjVOR1lZWGZyNHFYOEVVanFWZ0RJcE1uNFFieEJmYlNtWWdXZEdHUGpBNmU3Q0FYUGlxUC9JMHgxR2dJQzZFL2hGL0UiLCJtYWMiOiIzZmZkNDM2NmFkNmRjM2FhNGZhOTI3ZmY1YWRhZWJiZGU2NTUzNzY4N2E5NTJiNWU5MzJhNzc4MmYzY2IxZWExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-539039967\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1867212217 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58894</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6InNJa0FtYXJsaW9ET0Q3eG9Odmk1Qnc9PSIsInZhbHVlIjoibjM2T0c3QlIzVlpVbEhOeldmcmkrUTBPUlFBNHNqaHByRno5WEZYcGphMlBKUVUvclAzUkcvOWF6dlN5bFFIR2lEZzJWUC9taVFqNUFLNUNiMUZFMXFUYVZITHgyVUloZkhhTDFQeExoTk12eVlxK0VXcEdHZkZ5cDZ4YjdWR2MiLCJtYWMiOiJiYmM4MTBiYWI5MmE0Yzc1NmNmNWE4NWYyMmEwZjVlMmNkZjBlNGFjZGRhOTk5NzQ0ZGRmMmVjZjVmM2ZkYTgxIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkVvMnRCaTlOeitNRnF4VFh2RjU3TGc9PSIsInZhbHVlIjoibjEzV00vN21iREpQTDRsVkIzMVZQUTVlS1VZbWFPbFhrMHVZL1JlVGRXT1h6VUVaakdYSmhnVXVSQll3MjVOR1lZWGZyNHFYOEVVanFWZ0RJcE1uNFFieEJmYlNtWWdXZEdHUGpBNmU3Q0FYUGlxUC9JMHgxR2dJQzZFL2hGL0UiLCJtYWMiOiIzZmZkNDM2NmFkNmRjM2FhNGZhOTI3ZmY1YWRhZWJiZGU2NTUzNzY4N2E5NTJiNWU5MzJhNzc4MmYzY2IxZWExIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812394.4505</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812394</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867212217\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1986487271 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986487271\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-801591410 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:33:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklLL1hwMlE0QzNRdEJuRTNCbnpscnc9PSIsInZhbHVlIjoid3BPZDRYem5GMHJaRmIyV2h1dXVETnB5bnl5enVOS0N4MDNscGFxODhwZDQ5WWg1OW9WZVlUTWNmM2x2YTgyNTlDbUpXTkNSdFlHT0lEb0lIZWhVNmg0U1R1L25rdGhmYXc1bjZpUzE4MkZDeTBTdDNuNi8rU1dCU3RzTkQxcFkiLCJtYWMiOiIzMTJmNDQ2NDA0Mjk1YWRkYmQ4M2RiOTJlNzljYTVhMjAxN2YwMmNjYTc3YzQ3ZGY5OTcwYjE3Y2IxZTFmNDQ5IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:14 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6Imk4ZjBsbHhCcW8ra1pISmZ0bTF1eVE9PSIsInZhbHVlIjoiTkFpNi9TQkNWbDZXOWhPa3NwQWtEWUxQR05Kb1llT3BIempvbGNlWG5NcVlVNml1R3lMNFRzb29aVzZHMmpjODd1VStkVysvcDc3WEI2UXR0c0xhZ2NOejRXYTBsWnErUnVCemh4YWdmVVhnclZhenBIZ0JVSk4vTnhMMUxBZjIiLCJtYWMiOiJjNDI0M2YzYmZjY2E0MTcyOGFiMzc2ODg2Mjk1NmRiMjI1Yjg0NjI2YmIxNzYwY2MzZTlmNjU0ODc0YjljYTRlIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:14 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklLL1hwMlE0QzNRdEJuRTNCbnpscnc9PSIsInZhbHVlIjoid3BPZDRYem5GMHJaRmIyV2h1dXVETnB5bnl5enVOS0N4MDNscGFxODhwZDQ5WWg1OW9WZVlUTWNmM2x2YTgyNTlDbUpXTkNSdFlHT0lEb0lIZWhVNmg0U1R1L25rdGhmYXc1bjZpUzE4MkZDeTBTdDNuNi8rU1dCU3RzTkQxcFkiLCJtYWMiOiIzMTJmNDQ2NDA0Mjk1YWRkYmQ4M2RiOTJlNzljYTVhMjAxN2YwMmNjYTc3YzQ3ZGY5OTcwYjE3Y2IxZTFmNDQ5IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6Imk4ZjBsbHhCcW8ra1pISmZ0bTF1eVE9PSIsInZhbHVlIjoiTkFpNi9TQkNWbDZXOWhPa3NwQWtEWUxQR05Kb1llT3BIempvbGNlWG5NcVlVNml1R3lMNFRzb29aVzZHMmpjODd1VStkVysvcDc3WEI2UXR0c0xhZ2NOejRXYTBsWnErUnVCemh4YWdmVVhnclZhenBIZ0JVSk4vTnhMMUxBZjIiLCJtYWMiOiJjNDI0M2YzYmZjY2E0MTcyOGFiMzc2ODg2Mjk1NmRiMjI1Yjg0NjI2YmIxNzYwY2MzZTlmNjU0ODc0YjljYTRlIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-801591410\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1609337558 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609337558\", {\"maxDepth\":0})</script>\n"}}