{"__meta": {"id": "X4bd56c377e8f09e65baf0444e704002d", "datetime": "2025-07-06 14:28:07", "utime": 1751812087.699879, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:28:07] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812087.697783, "collector": "log"}, {"message": "[14:28:07] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812087.697797, "collector": "log"}, {"message": "[14:28:07] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812087.697897, "collector": "log"}, {"message": "[14:28:07] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812087.69791, "collector": "log"}]}, "time": {"start": 1751812087.664465, "end": 1751812087.69989, "duration": 0.03542494773864746, "duration_str": "35.42ms", "measures": [{"label": "Booting", "start": 1751812087.664465, "relative_start": 0, "end": 1751812087.697499, "relative_end": 1751812087.697499, "duration": 0.03303408622741699, "duration_str": "33.03ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812087.697622, "relative_start": 0.0331571102142334, "end": 1751812087.699891, "relative_end": 1.1920928955078125e-06, "duration": 0.0022690296173095703, "duration_str": "2.27ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2731488, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-1167425970 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1167425970\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-7448699 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-7448699\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-891398160 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891398160\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1476428427 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IjhITWRaQ0Fhd0Y1cTFqeXVTWFJVWVE9PSIsInZhbHVlIjoiNEl3TzJOSHprME1PMzcwNVkxcGR5cDh5ZytORVB4cnZVS2xmSGtvSW9FSUlVeVFSMkZSQWNFS0RZOWdFZXJXSUQwQjIxbWV5eTlQaFkwYTlLSk13QUI5ZnY2MHUrZllURkFINnpGRm1ZL0ZpSHliTnlJTHFpbUhLR1NXUVVaSVEiLCJtYWMiOiI1NjQ3NzUxZTk2NDMyNTFhMjIzMjU4MWQ5OTM5NDgzZjUwNTI1ZmRkZTA1MzVjNmZiY2U5NDUxNzI0YzhiNDI5IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkhOMDYvM29jQ3ROU0h2T3Q5WDZteEE9PSIsInZhbHVlIjoiQWZvYVlvZDVxeG90YnZSdElCS2NYT1JGdi9rVDIrYURqZWlJOWFMSE1MMXdLbTY0NnN5d28vZkh5bWs3ektEdm5zSTJVb21zM0VNUTg2bVV5VzQwMUZRL2VWekRYWHQzdmZqK241a3JsQlJBMTRBenptZkpKNFoweVR2bUYvcUIiLCJtYWMiOiIyODQyNzcxZWNmMjI1ZWM0NTBjNTRjYzkxZWY3ZTkzOWExNGUxOTM2NGYzY2ExZGRiODUzNDgzYzVkNmI3Zjk1IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1476428427\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-148985039 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58043</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IjhITWRaQ0Fhd0Y1cTFqeXVTWFJVWVE9PSIsInZhbHVlIjoiNEl3TzJOSHprME1PMzcwNVkxcGR5cDh5ZytORVB4cnZVS2xmSGtvSW9FSUlVeVFSMkZSQWNFS0RZOWdFZXJXSUQwQjIxbWV5eTlQaFkwYTlLSk13QUI5ZnY2MHUrZllURkFINnpGRm1ZL0ZpSHliTnlJTHFpbUhLR1NXUVVaSVEiLCJtYWMiOiI1NjQ3NzUxZTk2NDMyNTFhMjIzMjU4MWQ5OTM5NDgzZjUwNTI1ZmRkZTA1MzVjNmZiY2U5NDUxNzI0YzhiNDI5IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkhOMDYvM29jQ3ROU0h2T3Q5WDZteEE9PSIsInZhbHVlIjoiQWZvYVlvZDVxeG90YnZSdElCS2NYT1JGdi9rVDIrYURqZWlJOWFMSE1MMXdLbTY0NnN5d28vZkh5bWs3ektEdm5zSTJVb21zM0VNUTg2bVV5VzQwMUZRL2VWekRYWHQzdmZqK241a3JsQlJBMTRBenptZkpKNFoweVR2bUYvcUIiLCJtYWMiOiIyODQyNzcxZWNmMjI1ZWM0NTBjNTRjYzkxZWY3ZTkzOWExNGUxOTM2NGYzY2ExZGRiODUzNDgzYzVkNmI3Zjk1IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812087.6645</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812087</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-148985039\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-785961160 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-785961160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-311792764 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:28:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJEUmwzVVpGWW53cG9OTVRONGtHYkE9PSIsInZhbHVlIjoibDN2bEloZ0pmQlpsWnZRZG9tZXZreERCSEhFTEs4ck9nWFJBOEpaSUVWMTNNRVJNSTR1NDhQa00yZFhTcnpBcS9WdkY0c25vbUoweHlZakt3N3BUSUVEMUhCMzgxRkZiU1huZ2Q0SXd6YzZLd0t1Y2ZFN012eUdzb1dvREtsR2oiLCJtYWMiOiIxODhjMmZlODE1ZTNlZDY2NDJkYzVmZDRiNzNmODRhYmFkOWZhYWRkZjNiNzA5ZWE5NzEyZDJiMjYwMDdhNDc5IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:28:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6InlaOE8rcGQ2bklrVUwyQUdGeTdGd1E9PSIsInZhbHVlIjoiMnhUZWk3ckE2cTVjVStCOGdmdkJibENQUm5vS0pqVGs5T3JoOFkweXNMd0l4a3QwTTg5eFEwTWlXSm1jWUFYc2h6YmJ3YUl3NGVFbnBFZm5rN1I5bm4zMFpOK0QreVpydjFSUkM3TXpTeDdmVG4wajF2ZTFQc3NIRzY5STgvOVgiLCJtYWMiOiIyYjI2NjdlNmJlZjRkYWI4NjBmNDI2Yjc5NDY4NTkyNzU1OGIwNDJiNTJjZjQ0ZmJiMmQzZTliNGU2MTJjNzc2IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:28:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJEUmwzVVpGWW53cG9OTVRONGtHYkE9PSIsInZhbHVlIjoibDN2bEloZ0pmQlpsWnZRZG9tZXZreERCSEhFTEs4ck9nWFJBOEpaSUVWMTNNRVJNSTR1NDhQa00yZFhTcnpBcS9WdkY0c25vbUoweHlZakt3N3BUSUVEMUhCMzgxRkZiU1huZ2Q0SXd6YzZLd0t1Y2ZFN012eUdzb1dvREtsR2oiLCJtYWMiOiIxODhjMmZlODE1ZTNlZDY2NDJkYzVmZDRiNzNmODRhYmFkOWZhYWRkZjNiNzA5ZWE5NzEyZDJiMjYwMDdhNDc5IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:28:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6InlaOE8rcGQ2bklrVUwyQUdGeTdGd1E9PSIsInZhbHVlIjoiMnhUZWk3ckE2cTVjVStCOGdmdkJibENQUm5vS0pqVGs5T3JoOFkweXNMd0l4a3QwTTg5eFEwTWlXSm1jWUFYc2h6YmJ3YUl3NGVFbnBFZm5rN1I5bm4zMFpOK0QreVpydjFSUkM3TXpTeDdmVG4wajF2ZTFQc3NIRzY5STgvOVgiLCJtYWMiOiIyYjI2NjdlNmJlZjRkYWI4NjBmNDI2Yjc5NDY4NTkyNzU1OGIwNDJiNTJjZjQ0ZmJiMmQzZTliNGU2MTJjNzc2IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:28:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311792764\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1172324904 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172324904\", {\"maxDepth\":0})</script>\n"}}