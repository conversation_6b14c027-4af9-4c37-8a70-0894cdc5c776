{"__meta": {"id": "X07bb957c3402f98f2b8be80019340619", "datetime": "2025-07-06 14:33:24", "utime": 1751812404.619034, "method": "GET", "uri": "/support/chat/messages", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:33:24] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812404.612718, "collector": "log"}, {"message": "[14:33:24] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812404.612731, "collector": "log"}, {"message": "[14:33:24] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812404.612846, "collector": "log"}, {"message": "[14:33:24] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812404.612859, "collector": "log"}]}, "time": {"start": 1751812404.589621, "end": 1751812404.619042, "duration": 0.029420852661132812, "duration_str": "29.42ms", "measures": [{"label": "Booting", "start": 1751812404.589621, "relative_start": 0, "end": 1751812404.612339, "relative_end": 1751812404.612339, "duration": 0.022717952728271484, "duration_str": "22.72ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812404.612487, "relative_start": 0.022866010665893555, "end": 1751812404.619043, "relative_end": 1.1920928955078125e-06, "duration": 0.006556034088134766, "duration_str": "6.56ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3217912, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET support/chat/messages", "middleware": "web", "controller": "Modules\\Support\\Controllers\\SupportChatController@getMessages", "namespace": "Modules\\Support\\Controllers", "prefix": "/support", "where": [], "as": "support.chat.messages", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Support/Controllers/SupportChatController.php&line=155\">modules/Support/Controllers/SupportChatController.php:155-210</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00233, "accumulated_duration_str": "2.33ms", "statements": [{"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` in ('waiting', 'active') and `support_chat_conversations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "waiting", "active"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 281}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 158}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00182, "duration_str": "1.82ms", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:281", "connection": "megafly"}, {"sql": "select * from `support_chat_conversations` where `session_id` = 'dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT' and `status` = 'closed' and `updated_at` >= '2025-07-06 14:23:24' and `support_chat_conversations`.`deleted_at` is null order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": ["dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT", "closed", "2025-07-06 14:23:24"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 301}, {"index": 16, "namespace": null, "name": "/modules/Support/Controllers/SupportChatController.php", "line": 163}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "/modules/Support/Controllers/SupportChatController.php:301", "connection": "megafly"}]}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/support/chat/messages", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-162335834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-162335834\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-150713443 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IkdKanBvSmpsalIrYVpETGZxUU9YNkE9PSIsInZhbHVlIjoiNlVJd2RpdlI2R1U5ZklQQktRNk16RDh6alczcTJMc0F3SHFDMjZCOHVoc1hjcHZoS1dRV3Y2aW84cFpxTEVsSGhMa09HUTErZ01HZk9TbWU0dUhNYnJQNXhJR0FWTWpwRmhwTGJTTlhEeHFtekpoUmNPZE5QYzhQMldLL1ErTjIiLCJtYWMiOiI3N2VmYmY5N2RjYmU3NGI3YTAyZWU5YWMyNzcxMDRhNDcyOTRiNGE5ZTE0YmVkODFlOGZhNTkxYmUzMjE1MmQ2IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6ImNtRkVCNTR3M2g5aXRSSnQ5RjM2TUE9PSIsInZhbHVlIjoia2o1L1pXVERpNklWd1M2OVRWY2lvZmR0QndVbFVaeHBBVnhHWm1DWUtzSW42ODlNMVFwamN5amF6eEVyb29BMDNqUEUyek5Qc292TWNOTkwvZlhxOU43Y0lqQmg2TVB0SmVKTzNqWExCT1Vyb1FqZDFmZkZTMVRZbmF2amZwbEgiLCJtYWMiOiIxMTU0NDlhZDQzYTE5YjYwOTVmODk5Y2E4ODMwYjEzMzJlOGU1MjNiMzYyM2MzN2M0YTMwNzYzMmRmNmM5MzQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150713443\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1010222468 data-indent-pad=\"  \"><span class=sf-dump-note>array:28</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58964</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/support/chat/messages</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IkdKanBvSmpsalIrYVpETGZxUU9YNkE9PSIsInZhbHVlIjoiNlVJd2RpdlI2R1U5ZklQQktRNk16RDh6alczcTJMc0F3SHFDMjZCOHVoc1hjcHZoS1dRV3Y2aW84cFpxTEVsSGhMa09HUTErZ01HZk9TbWU0dUhNYnJQNXhJR0FWTWpwRmhwTGJTTlhEeHFtekpoUmNPZE5QYzhQMldLL1ErTjIiLCJtYWMiOiI3N2VmYmY5N2RjYmU3NGI3YTAyZWU5YWMyNzcxMDRhNDcyOTRiNGE5ZTE0YmVkODFlOGZhNTkxYmUzMjE1MmQ2IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6ImNtRkVCNTR3M2g5aXRSSnQ5RjM2TUE9PSIsInZhbHVlIjoia2o1L1pXVERpNklWd1M2OVRWY2lvZmR0QndVbFVaeHBBVnhHWm1DWUtzSW42ODlNMVFwamN5amF6eEVyb29BMDNqUEUyek5Qc292TWNOTkwvZlhxOU43Y0lqQmg2TVB0SmVKTzNqWExCT1Vyb1FqZDFmZkZTMVRZbmF2amZwbEgiLCJtYWMiOiIxMTU0NDlhZDQzYTE5YjYwOTVmODk5Y2E4ODMwYjEzMzJlOGU1MjNiMzYyM2MzN2M0YTMwNzYzMmRmNmM5MzQ5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812404.5896</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812404</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010222468\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-798280148 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dn65CGqR5CoxWUGQAAXZeD5favIzP2ic3gZuKndT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798280148\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1222064626 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:33:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkN2eGk4OGFqTFZ3SXo0RFVMSGorS1E9PSIsInZhbHVlIjoiaDBiek9VUHRocFY5ZkxjVVdicFRBWE5KMGd1ZDZnOHJDK25nNVoxc0hXeUpJais4cVpzeWxSZmlkMkw5MEU5bGZHdFhOdEZGQlJKem9vWGxNWkloUmxpYzN1ejVxcmYwb3IrdHRhejdwNjhDcnlyRFE2eFFFZHBCRUNuamdtQ2giLCJtYWMiOiJlMGMyYzRiZmE5ZjE4MDU3ZWJhM2I2ODA5MzMxMGMxMzQ3NmJkYTRiMWVkOTI5MzI3OGU4Mzk5ZmVhY2FhZmJmIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:24 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IjZxQ25BNWVHNW9LN3J5djExUlFSMlE9PSIsInZhbHVlIjoiV0NlbFJYNi83QThVRFQvM3RYVzJHVXdIQ2l2M2JyZUZwNGJOdHV2alV0Nm1rcE52Q0Zsd3FmOHZRMUxQUjFxWnphQzVuakhxQ3R5YkVQaTBmUkNQQlJlRDJXbXZHWUpjbDFING50c0QzUDV1YU5rUVUzMGJ5TmFYUzRhSjBjQ2MiLCJtYWMiOiJkNzA1NGVmMDhjNWVkMGI3M2Q4ZTk3ZmU4NGUwOTMzYjAxNTA2ZmY0M2Y0YzBjYjYzOWRkZTg3Y2FlMGY1ODQ5IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:33:24 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkN2eGk4OGFqTFZ3SXo0RFVMSGorS1E9PSIsInZhbHVlIjoiaDBiek9VUHRocFY5ZkxjVVdicFRBWE5KMGd1ZDZnOHJDK25nNVoxc0hXeUpJais4cVpzeWxSZmlkMkw5MEU5bGZHdFhOdEZGQlJKem9vWGxNWkloUmxpYzN1ejVxcmYwb3IrdHRhejdwNjhDcnlyRFE2eFFFZHBCRUNuamdtQ2giLCJtYWMiOiJlMGMyYzRiZmE5ZjE4MDU3ZWJhM2I2ODA5MzMxMGMxMzQ3NmJkYTRiMWVkOTI5MzI3OGU4Mzk5ZmVhY2FhZmJmIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IjZxQ25BNWVHNW9LN3J5djExUlFSMlE9PSIsInZhbHVlIjoiV0NlbFJYNi83QThVRFQvM3RYVzJHVXdIQ2l2M2JyZUZwNGJOdHV2alV0Nm1rcE52Q0Zsd3FmOHZRMUxQUjFxWnphQzVuakhxQ3R5YkVQaTBmUkNQQlJlRDJXbXZHWUpjbDFING50c0QzUDV1YU5rUVUzMGJ5TmFYUzRhSjBjQ2MiLCJtYWMiOiJkNzA1NGVmMDhjNWVkMGI3M2Q4ZTk3ZmU4NGUwOTMzYjAxNTA2ZmY0M2Y0YzBjYjYzOWRkZTg3Y2FlMGY1ODQ5IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:33:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222064626\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gjQ0ivVeVv6YUokoqQEy8EeoRiGL4jCRh2WfCtwQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}