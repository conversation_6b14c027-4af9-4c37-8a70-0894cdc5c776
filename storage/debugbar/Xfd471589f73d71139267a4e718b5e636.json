{"__meta": {"id": "Xfd471589f73d71139267a4e718b5e636", "datetime": "2025-07-06 14:31:31", "utime": 1751812291.493959, "method": "GET", "uri": "/custom-css", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.491555, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.491569, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.491671, "collector": "log"}, {"message": "[14:31:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751812291.491687, "collector": "log"}]}, "time": {"start": 1751812291.466803, "end": 1751812291.493969, "duration": 0.027165889739990234, "duration_str": "27.17ms", "measures": [{"label": "Booting", "start": 1751812291.466803, "relative_start": 0, "end": 1751812291.491274, "relative_end": 1751812291.491274, "duration": 0.024471044540405273, "duration_str": "24.47ms", "params": [], "collector": null}, {"label": "Application", "start": 1751812291.491393, "relative_start": 0.024590015411376953, "end": 1751812291.493971, "relative_end": 2.1457672119140625e-06, "duration": 0.0025780200958251953, "duration_str": "2.58ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 2730216, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET custom-css", "middleware": "web", "controller": "Modules\\Core\\Controllers\\StyleController@customCss", "namespace": "Modules\\Core\\Controllers", "prefix": "", "where": [], "as": "core.style.customCss", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Core/Controllers/StyleController.php&line=9\">modules/Core/Controllers/StyleController.php:9-16</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/custom-css", "status_code": "<pre class=sf-dump id=sf-dump-1355629345 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1355629345\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/css; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-649588551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-649588551\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1464376830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1464376830\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1464256676 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6InYwSkdCcC9FWkxqRE9IdVFUZ0d1eHc9PSIsInZhbHVlIjoiOTliM0JLNnBlSStSYVFwSzVBVXk1VnFicXN1N2NmSE9SbGFQdW4vcGl4cjBNUVpZOUxQSVpLZERpQzI3N3pJU3BOWkdkekNzNUEzelZ0b1V4RFdzbVRNdCs2MVUweHc4UmdSViszSk5zUk1VOHA2Qk5CL3Y2S2tVbnRUV0k3L3IiLCJtYWMiOiIwNjY4YTFkYzcyMTE4MWIxNTkxZDY1M2U4ZTAyNzFjMGJiYWViOWE1NjZmY2UzN2I1NjkwNGU3NGU4MTczMDUyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkRjdWpjV3JQY3BzL0IvT0hzUTBuTmc9PSIsInZhbHVlIjoiaWJNYXlQSHN6dU4rMXNXc256T0I3OXJsd0Fxc0JmbUY1OVZyL3JCaHROem1ROXFUd3V4c1BySk96WXNHL1ZSb2hJcXZoeTRPalEyYVNKTVllSm9UNTJhUWdLV1hKVEtyZElNUnFIZWFkNERyai92VXJoSm9JdVRPQkYwYk93ZjciLCJtYWMiOiJlNGM5NmY1ZjAwOGU3YTMwMDI1NGE4NTA0ODgwNGFjZTBkZjVlMGE1MTg4MTFlMDAyMjQ4MTUwNDU3OWU5NjFkIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464256676\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1056686590 data-indent-pad=\"  \"><span class=sf-dump-note>array:27</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58568</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"11 characters\">/custom-css</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/index.php/custom-css</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">style</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"18 characters\">text/css,*/*;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6InYwSkdCcC9FWkxqRE9IdVFUZ0d1eHc9PSIsInZhbHVlIjoiOTliM0JLNnBlSStSYVFwSzVBVXk1VnFicXN1N2NmSE9SbGFQdW4vcGl4cjBNUVpZOUxQSVpLZERpQzI3N3pJU3BOWkdkekNzNUEzelZ0b1V4RFdzbVRNdCs2MVUweHc4UmdSViszSk5zUk1VOHA2Qk5CL3Y2S2tVbnRUV0k3L3IiLCJtYWMiOiIwNjY4YTFkYzcyMTE4MWIxNTkxZDY1M2U4ZTAyNzFjMGJiYWViOWE1NjZmY2UzN2I1NjkwNGU3NGU4MTczMDUyIiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkRjdWpjV3JQY3BzL0IvT0hzUTBuTmc9PSIsInZhbHVlIjoiaWJNYXlQSHN6dU4rMXNXc256T0I3OXJsd0Fxc0JmbUY1OVZyL3JCaHROem1ROXFUd3V4c1BySk96WXNHL1ZSb2hJcXZoeTRPalEyYVNKTVllSm9UNTJhUWdLV1hKVEtyZElNUnFIZWFkNERyai92VXJoSm9JdVRPQkYwYk93ZjciLCJtYWMiOiJlNGM5NmY1ZjAwOGU3YTMwMDI1NGE4NTA0ODgwNGFjZTBkZjVlMGE1MTg4MTFlMDAyMjQ4MTUwNDU3OWU5NjFkIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjZUclNjMjJKVmNFQVIveTU4ZmRkVnc9PSIsInZhbHVlIjoiZlFaNU9Wa2FmVlVVT0k0SENvSXF4RU9jdC9KZU51UER6SCt0K215Nm1GM3RIUFNJYnJ0RnVZZXNFZmRrS1lNMXpPVDMyQWtQM3NuR3RHNm0yb3F4MnhrT3gvMVAwZDZ4VFBaSXFYdkZqNnM2b0ZnTUdEbkh5Mm1jeGdKRDBQRVEiLCJtYWMiOiI5NTg1MzZjMjgwYWY2NTA2Yjk0OGUwZWQ3MzE5MTBiM2Y3N2FiMGRmZjUyODc3YjhjZWE4YTUxZjdmNjUzY2QzIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751812291.4668</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751812291</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056686590\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1230789127 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230789127\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-762705974 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">text/css; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 14:31:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjNSYzM4alVXeUw3cVRjZklOd3UxRkE9PSIsInZhbHVlIjoiSFNYUlJpZCt3bjFTcXlaSnd1bVpaNU1Nd0s2NUNjbSt4T1BtVGtZMW9FWEpycjd4bkNXVTZvU3h0S2ZBb201MW5uTWpWRmZnTDJBWVFBaXFkTzJHQkZQaFRGZGtpdmVwOGtTdk0yR3dKVzZ1ZUJQalBZYzJ3TG9pd2JrRXpYZ2giLCJtYWMiOiJkZGU1ODY5ZmE5MGRiODJkOTI3ZjI1Njg4NGI1MzVhMDEzNzcxZmRjNTM5ZjcwN2JkNGFjZGI2ZmI3MGQ1NTMzIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6Ild2dzQ5VWRkZnY1V3RvL082enM3eHc9PSIsInZhbHVlIjoiQ3VUYWFjNTVYR0dQVVFtTU95YVdnK0JXWmFaNERXeWNPdGlhdzljWUM1dkQ1eGJrN05DNElESFFYSTV3WkZoVUVINGJMWXl3RExEOHlKWUo3NUhreittbjJtV3Q5R1RZTDlRZS91blBKQXpPUWM2eFJTNkxtT2hNUjFGRjJBemEiLCJtYWMiOiI5ODRkMjY0NWFlNjEwOWJhMjM3NTM2MDExNzAyMWI2NDYxYTc2ZjBlM2I4MGRjNDY4NDRkZjhmOWNkNTZiZmZhIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 16:31:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjNSYzM4alVXeUw3cVRjZklOd3UxRkE9PSIsInZhbHVlIjoiSFNYUlJpZCt3bjFTcXlaSnd1bVpaNU1Nd0s2NUNjbSt4T1BtVGtZMW9FWEpycjd4bkNXVTZvU3h0S2ZBb201MW5uTWpWRmZnTDJBWVFBaXFkTzJHQkZQaFRGZGtpdmVwOGtTdk0yR3dKVzZ1ZUJQalBZYzJ3TG9pd2JrRXpYZ2giLCJtYWMiOiJkZGU1ODY5ZmE5MGRiODJkOTI3ZjI1Njg4NGI1MzVhMDEzNzcxZmRjNTM5ZjcwN2JkNGFjZGI2ZmI3MGQ1NTMzIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6Ild2dzQ5VWRkZnY1V3RvL082enM3eHc9PSIsInZhbHVlIjoiQ3VUYWFjNTVYR0dQVVFtTU95YVdnK0JXWmFaNERXeWNPdGlhdzljWUM1dkQ1eGJrN05DNElESFFYSTV3WkZoVUVINGJMWXl3RExEOHlKWUo3NUhreittbjJtV3Q5R1RZTDlRZS91blBKQXpPUWM2eFJTNkxtT2hNUjFGRjJBemEiLCJtYWMiOiI5ODRkMjY0NWFlNjEwOWJhMjM3NTM2MDExNzAyMWI2NDYxYTc2ZjBlM2I4MGRjNDY4NDRkZjhmOWNkNTZiZmZhIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 16:31:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762705974\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-215895750 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8001/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215895750\", {\"maxDepth\":0})</script>\n"}}