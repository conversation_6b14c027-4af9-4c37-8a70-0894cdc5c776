{"__meta": {"id": "Xcc3aeaf011ef86d903621e15a0fd1f62", "datetime": "2025-07-06 15:25:46", "utime": 1751815546.541704, "method": "GET", "uri": "/location/search/searchForSelect2?search=je", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[15:25:46] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815546.532587, "collector": "log"}, {"message": "[15:25:46] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815546.5326, "collector": "log"}, {"message": "[15:25:46] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751815546.532717, "collector": "log"}, {"message": "[15:25:46] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751815546.532731, "collector": "log"}]}, "time": {"start": 1751815546.506303, "end": 1751815546.541714, "duration": 0.03541088104248047, "duration_str": "35.41ms", "measures": [{"label": "Booting", "start": 1751815546.506303, "relative_start": 0, "end": 1751815546.532229, "relative_end": 1751815546.532229, "duration": 0.025925874710083008, "duration_str": "25.93ms", "params": [], "collector": null}, {"label": "Application", "start": 1751815546.532358, "relative_start": 0.026054859161376953, "end": 1751815546.541715, "relative_end": 9.5367431640625e-07, "duration": 0.009356975555419922, "duration_str": "9.36ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3311608, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET location/search/searchForSelect2", "middleware": "web", "controller": "Modules\\Location\\Controllers\\LocationController@searchForSelect2", "namespace": "Modules\\Location\\Controllers", "prefix": "/location", "where": [], "as": "location.searchForSelect", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Location/Controllers/LocationController.php&line=44\">modules/Location/Controllers/LocationController.php:44-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0053, "accumulated_duration_str": "5.3ms", "statements": [{"sql": "select `airports`.*, `airports`.`name` as `title` from `airports` where `airports`.`name` like '%je%' or `airports`.`code` like '%je%' order by `name` asc limit 20", "type": "query", "params": [], "bindings": ["%je%", "%je%"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/modules/Location/Controllers/LocationController.php", "line": 61}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "duration": 0.0053, "duration_str": "5.3ms", "stmt_id": "/modules/Location/Controllers/LocationController.php:61", "connection": "megafly"}]}, "models": {"data": {"App\\Models\\Airports": 6}, "count": 6}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/location/search/searchForSelect2", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">je</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2080736378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2080736378\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1659415636 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IlJXdjhtOFRjWUx4T01SZEk5TFAzcFE9PSIsInZhbHVlIjoiRnhZb1lIcFJ6Wjd5OU5NelB1b0dNc3dTdlB5Ny9qdy9kV2xkc0JaTkdzNUNsZWJITGRKR3NnMU9pbW1WQSt3QWp6alRiS3BINnlyUnJoNGZhQ3NTWm04b0hveGR3VSs0NlM0cGtTVHBLVmlMNG1EU1R6NGh6L1NKc3NObUtmVVkiLCJtYWMiOiI4MDllMjQ5Y2Q2ZTE2OTg2MmZlYmQ5ZDc1YTc2MzY2OWNhM2MyMzk4NzdjOGU3OGExZTZkYTg1OTkxYzAyNWM2IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ik44UEFWMnh3RGNBOHp2Qnl0NUQxOHc9PSIsInZhbHVlIjoiZG0xVTBWOEhUMGlDdUQyVDlseDBWTTFDUXkzWGx0WlZDblhaaFlPSWtEOUVSUE52MXg1YWtBVHo3SVR0TkFSVHVXdlJ5MVY2VkNJd2ppRjFqQWphSDhyZUIxdy9Lb0FrTHNMWDg5ZzczVVV3bXhzVHNFNy9QSld4eVhac1BBWGgiLCJtYWMiOiJlM2YwNzFmNTUyMzU4YjU0MDllNDg2ZWYwN2Q4ZjcyODk2NTg4ZGUyZjg0NjhhZjYwZGNiMzllOTQxZDM5NDdlIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjBNV1JQTzZzVVVmVWhiUExGUDJDV3c9PSIsInZhbHVlIjoicG9Vb3ZTVU8yYk9LcThjRURKTHFzdE80eXRGQVpPdkxXVzcyVGlGaEFrd2dyVzNuQlNuYlNUdkxucjlkbFc5ZG5CeExPSVZvM0p5WDJPZ3ozenJTdVRpQ3ZCVktDK2FvN2NkMS9UbDhqTUZPa05SVzk3d3YzVTRDMlJFQVQ1c2EiLCJtYWMiOiI1NGFkZjA5ZjU4NTNhNDI2NDcwNWM5ZjE1MGZmMmI0M2EzYmU0ZjFhYWQ1ZDk3YzAxOGRkMDMzMjEwM2JiNjUwIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1659415636\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1551896758 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">55997</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/location/search/searchForSelect2?search=je</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/index.php/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"9 characters\">search=je</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IlJXdjhtOFRjWUx4T01SZEk5TFAzcFE9PSIsInZhbHVlIjoiRnhZb1lIcFJ6Wjd5OU5NelB1b0dNc3dTdlB5Ny9qdy9kV2xkc0JaTkdzNUNsZWJITGRKR3NnMU9pbW1WQSt3QWp6alRiS3BINnlyUnJoNGZhQ3NTWm04b0hveGR3VSs0NlM0cGtTVHBLVmlMNG1EU1R6NGh6L1NKc3NObUtmVVkiLCJtYWMiOiI4MDllMjQ5Y2Q2ZTE2OTg2MmZlYmQ5ZDc1YTc2MzY2OWNhM2MyMzk4NzdjOGU3OGExZTZkYTg1OTkxYzAyNWM2IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6Ik44UEFWMnh3RGNBOHp2Qnl0NUQxOHc9PSIsInZhbHVlIjoiZG0xVTBWOEhUMGlDdUQyVDlseDBWTTFDUXkzWGx0WlZDblhaaFlPSWtEOUVSUE52MXg1YWtBVHo3SVR0TkFSVHVXdlJ5MVY2VkNJd2ppRjFqQWphSDhyZUIxdy9Lb0FrTHNMWDg5ZzczVVV3bXhzVHNFNy9QSld4eVhac1BBWGgiLCJtYWMiOiJlM2YwNzFmNTUyMzU4YjU0MDllNDg2ZWYwN2Q4ZjcyODk2NTg4ZGUyZjg0NjhhZjYwZGNiMzllOTQxZDM5NDdlIiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IjBNV1JQTzZzVVVmVWhiUExGUDJDV3c9PSIsInZhbHVlIjoicG9Vb3ZTVU8yYk9LcThjRURKTHFzdE80eXRGQVpPdkxXVzcyVGlGaEFrd2dyVzNuQlNuYlNUdkxucjlkbFc5ZG5CeExPSVZvM0p5WDJPZ3ozenJTdVRpQ3ZCVktDK2FvN2NkMS9UbDhqTUZPa05SVzk3d3YzVTRDMlJFQVQ1c2EiLCJtYWMiOiI1NGFkZjA5ZjU4NTNhNDI2NDcwNWM5ZjE1MGZmMmI0M2EzYmU0ZjFhYWQ1ZDk3YzAxOGRkMDMzMjEwM2JiNjUwIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751815546.5063</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751815546</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551896758\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1151170633 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151170633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-508739905 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 15:25:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Iks3L3dUa0Eza0MxUFBTUlExS2ZwRGc9PSIsInZhbHVlIjoidGhBMnhWVC9HQUwrTjlvMkhQOG1ZYjRuSmxIUHJtTXJ4ZnJHZWZ1bEpTVm1udUVzOFFpZGM0R3hKQmFtTnFXMmtEb0R6aTgxaHRUOFZpdTVQYXY3OTlSMWk3RXJOa3o4a05FQXVOclJjanVHeHFGOWdLNkRxR1FsMzgyMmtBY1EiLCJtYWMiOiI0ZjhmODVlNTQ3NzYzZTFlYmFkYjJkZGFiMDI4ZjQyZmQ4MTZlYWJiNDY1M2Y1NmViOTY5NmE5ZTc1NjNiYzg4IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:25:46 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6IjJIN3htbEd1R29rZFNablh5Rm14a3c9PSIsInZhbHVlIjoiOFg0Qjl4NUVqbm05M3V0a3F1SWdhUDVpNityaUxRNDFCNkV5STYrYzB1TGRDMExxZ1dUQkwzZnJ4cVpCdHcwVnRNVEZ2ajFQRk0rZ0tOS2dtWE5XTUl3MlMwMy9tbnh3RWlVcFpwZmRPRm1ZRHdpK3V6RVZnWFJ5Z0NMdmxuOWIiLCJtYWMiOiJmZDc3MDkzM2Q4MDhjNDhmMWRiNjNjNWIzZTRhZWU4NTY5ZDdlOTBmOWE4MDI4ZjUyY2QxODhlZWMwMDUxMThlIiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:25:46 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Iks3L3dUa0Eza0MxUFBTUlExS2ZwRGc9PSIsInZhbHVlIjoidGhBMnhWVC9HQUwrTjlvMkhQOG1ZYjRuSmxIUHJtTXJ4ZnJHZWZ1bEpTVm1udUVzOFFpZGM0R3hKQmFtTnFXMmtEb0R6aTgxaHRUOFZpdTVQYXY3OTlSMWk3RXJOa3o4a05FQXVOclJjanVHeHFGOWdLNkRxR1FsMzgyMmtBY1EiLCJtYWMiOiI0ZjhmODVlNTQ3NzYzZTFlYmFkYjJkZGFiMDI4ZjQyZmQ4MTZlYWJiNDY1M2Y1NmViOTY5NmE5ZTc1NjNiYzg4IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:25:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6IjJIN3htbEd1R29rZFNablh5Rm14a3c9PSIsInZhbHVlIjoiOFg0Qjl4NUVqbm05M3V0a3F1SWdhUDVpNityaUxRNDFCNkV5STYrYzB1TGRDMExxZ1dUQkwzZnJ4cVpCdHcwVnRNVEZ2ajFQRk0rZ0tOS2dtWE5XTUl3MlMwMy9tbnh3RWlVcFpwZmRPRm1ZRHdpK3V6RVZnWFJ5Z0NMdmxuOWIiLCJtYWMiOiJmZDc3MDkzM2Q4MDhjNDhmMWRiNjNjNWIzZTRhZWU4NTY5ZDdlOTBmOWE4MDI4ZjUyY2QxODhlZWMwMDUxMThlIiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:25:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508739905\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1380470708 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380470708\", {\"maxDepth\":0})</script>\n"}}