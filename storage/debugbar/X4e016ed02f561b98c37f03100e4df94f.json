{"__meta": {"id": "X4e016ed02f561b98c37f03100e4df94f", "datetime": "2025-07-06 15:41:31", "utime": 1751816491.022318, "method": "GET", "uri": "/location/search/searchForSelect2?search=cai", "ip": "127.0.0.1"}, "php": {"version": "8.2.28", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[15:41:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751816491.014207, "collector": "log"}, {"message": "[15:41:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\QueryFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751816491.01422, "collector": "log"}, {"message": "[15:41:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$cloner is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 23", "message_html": null, "is_string": false, "label": "warning", "time": 1751816491.014311, "collector": "log"}, {"message": "[15:41:31] LOG.warning: Creation of dynamic property Barryvdh\\Debugbar\\DataFormatter\\SimpleFormatter::$dumper is deprecated in /Users/<USER>/Projects/Mega-Fly/vendor/maximebf/debugbar/src/DebugBar/DataFormatter/DataFormatter.php on line 24", "message_html": null, "is_string": false, "label": "warning", "time": 1751816491.014324, "collector": "log"}]}, "time": {"start": 1751816490.981535, "end": 1751816491.022325, "duration": 0.04079008102416992, "duration_str": "40.79ms", "measures": [{"label": "Booting", "start": 1751816490.981535, "relative_start": 0, "end": 1751816491.013968, "relative_end": 1751816491.013968, "duration": 0.03243303298950195, "duration_str": "32.43ms", "params": [], "collector": null}, {"label": "Application", "start": 1751816491.014061, "relative_start": 0.03252601623535156, "end": 1751816491.022326, "relative_end": 9.5367431640625e-07, "duration": 0.008265018463134766, "duration_str": "8.27ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 3308600, "peak_usage_str": "3MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET location/search/searchForSelect2", "middleware": "web", "controller": "Modules\\Location\\Controllers\\LocationController@searchForSelect2", "namespace": "Modules\\Location\\Controllers", "prefix": "/location", "where": [], "as": "location.searchForSelect", "file": "<a href=\"phpstorm://open?file=/Users/<USER>/Projects/Mega-Fly/modules/Location/Controllers/LocationController.php&line=44\">modules/Location/Controllers/LocationController.php:44-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.004719999999999999, "accumulated_duration_str": "4.72ms", "statements": [{"sql": "select `airports`.*, `airports`.`name` as `title` from `airports` where `airports`.`name` like '%cai%' or `airports`.`code` like '%cai%' order by `name` asc limit 20", "type": "query", "params": [], "bindings": ["%cai%", "%cai%"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "/modules/Location/Controllers/LocationController.php", "line": 61}, {"index": 15, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "line": 205}], "duration": 0.004719999999999999, "duration_str": "4.72ms", "stmt_id": "/modules/Location/Controllers/LocationController.php:61", "connection": "megafly"}]}, "models": {"data": {"App\\Models\\Airports": 1}, "count": 1}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8001/support/chat/messages\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/location/search/searchForSelect2", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"3 characters\">cai</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2029712363 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2029712363\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1061166324 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IkFvSlJVRTJ2azNHODFSUjBuQ2ZRTHc9PSIsInZhbHVlIjoiQm92VGcwdnZ1SUlldEt5Ym1GVEgvTjBCSWVwU2lnVUZlUENxcXRCQVE5MWZJd2ZMOUNNOXdqSGQ4THNEN0VQRFIyQkZVZER5WldGOUdGcHhRSzJYVm9CY0tVeFEvZHRCWEhsN3o1Z24rVVBwN2plNkwyanNGU0ttQnk4YkNQYVoiLCJtYWMiOiIxOGU0Nzc3ZDY4MjNlM2IyYjE2YTc0OGZjMDQ4YWRlZjY0MTE3MDc1OWUzYzVmZTdhNGY5YzFmNjQzYmY2Y2I4IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkhkeGs5Q0Y5SHBNUFhIaXdCaFZ2d2c9PSIsInZhbHVlIjoiNTZ6TGZ6VTRZU0h2RDNuemI1NTQrUFBUYi91YmFzU2pDM3pua0R4dGQ5ZDFadWNOekRGaDdUWjRiRGFubFNwRU1SZFlpYnVMKy9HUTE1RnErcEVXMDBRS1o4QVZrc1ZLY0htemlleTFOUk1EZTBBcDZjZTZjU0I0b3dEeFdqWXYiLCJtYWMiOiIxMWExZmVhYTJjODI4YmJkYTVhZjMzZjg0ZjUwMTljYjM4NzM2ODg2NTI5NzNmODdjYzg5Nzc3NWYwYjJmOGM3IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IkRSZkVEMzlkMjhTZi9vZjRuUnk1UHc9PSIsInZhbHVlIjoiUndEWWl5aHowczY3TGVBSlY0TWJ5aHhqQ083V3lYUnNubVEySVhOZXNpZlNZemFvNTIrUm5HbXJPRWJHMHptZXdpcTNiOU5IVEwzS0pGeTFiTk01M3V2VkxmQ2VqTVkvdGxVNUVWYy9weU9Va2NMRUU2SGhIWkFtNWRIeTd6V1EiLCJtYWMiOiJiNTI5MzMxY2VmYWY3ZGY2NDFhMjU0M2UwNDI5MDc0ODQwYTJhODRlZGRmNTY3ZTNiYTQ5YmE2ODFlMmYwMTczIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061166324\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-704391029 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/Users/<USER>/Projects/Mega-Fly/public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58367</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.28 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8001</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"44 characters\">/location/search/searchForSelect2?search=cai</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"54 characters\">/Users/<USER>/Projects/Mega-Fly/public/index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"43 characters\">/index.php/location/search/searchForSelect2</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">search=cai</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8001</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8001/</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_PRIORITY</span>\" => \"<span class=sf-dump-str title=\"6 characters\">u=3, i</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1636 characters\">XSRF-TOKEN=eyJpdiI6IkFvSlJVRTJ2azNHODFSUjBuQ2ZRTHc9PSIsInZhbHVlIjoiQm92VGcwdnZ1SUlldEt5Ym1GVEgvTjBCSWVwU2lnVUZlUENxcXRCQVE5MWZJd2ZMOUNNOXdqSGQ4THNEN0VQRFIyQkZVZER5WldGOUdGcHhRSzJYVm9CY0tVeFEvZHRCWEhsN3o1Z24rVVBwN2plNkwyanNGU0ttQnk4YkNQYVoiLCJtYWMiOiIxOGU0Nzc3ZDY4MjNlM2IyYjE2YTc0OGZjMDQ4YWRlZjY0MTE3MDc1OWUzYzVmZTdhNGY5YzFmNjQzYmY2Y2I4IiwidGFnIjoiIn0%3D; mega_fly_session=eyJpdiI6IkhkeGs5Q0Y5SHBNUFhIaXdCaFZ2d2c9PSIsInZhbHVlIjoiNTZ6TGZ6VTRZU0h2RDNuemI1NTQrUFBUYi91YmFzU2pDM3pua0R4dGQ5ZDFadWNOekRGaDdUWjRiRGFubFNwRU1SZFlpYnVMKy9HUTE1RnErcEVXMDBRS1o4QVZrc1ZLY0htemlleTFOUk1EZTBBcDZjZTZjU0I0b3dEeFdqWXYiLCJtYWMiOiIxMWExZmVhYTJjODI4YmJkYTVhZjMzZjg0ZjUwMTljYjM4NzM2ODg2NTI5NzNmODdjYzg5Nzc3NWYwYjJmOGM3IiwidGFnIjoiIn0%3D; dosshr_session=eyJpdiI6IkRSZkVEMzlkMjhTZi9vZjRuUnk1UHc9PSIsInZhbHVlIjoiUndEWWl5aHowczY3TGVBSlY0TWJ5aHhqQ083V3lYUnNubVEySVhOZXNpZlNZemFvNTIrUm5HbXJPRWJHMHptZXdpcTNiOU5IVEwzS0pGeTFiTk01M3V2VkxmQ2VqTVkvdGxVNUVWYy9weU9Va2NMRUU2SGhIWkFtNWRIeTd6V1EiLCJtYWMiOiJiNTI5MzMxY2VmYWY3ZGY2NDFhMjU0M2UwNDI5MDc0ODQwYTJhODRlZGRmNTY3ZTNiYTQ5YmE2ODFlMmYwMTczIiwidGFnIjoiIn0%3D; booking_cookie_agreement_enable=eyJpdiI6ImVHbHFubGtvL014Q2ZTSWNKbEE4aWc9PSIsInZhbHVlIjoia3BJZG4xVHFZTGNqR3U1aEFpcDVMK0VNWlNUYW42REZUMU1FVGFMaWNUSUlISnhWcXM5SnNiU1kxRjZaMy9XUyIsIm1hYyI6IjNmOWI2N2JjMzBiYmI5YjJkYzFkMWMwYjU5NjIwYTljYmFhYzg3NTk2YTI4MjdmNzAzY2UyYTE3N2ExYjZhNTQiLCJ0YWciOiIifQ%3D%3D; locale=eyJpdiI6Ikk5blVCTEFoaVNaaVdjUEUzdnBVQkE9PSIsInZhbHVlIjoiWUF0MEtWSjJOL0NmVGlhSEpZNnVqd2lpYmo4Q0hHeEF3VDFodjE0MSt1MUNYWW1qaDJQWllvOG9rNmc2QnBxTCIsIm1hYyI6IjI4NzhiN2JiMmUxYjQ3N2NkYWZjZmZjZGI5NDViMWQxYjgxYmVhODlkZjdlMzFiNzYzYTgzODU3ZTk1YjA5NmUiLCJ0YWciOiIifQ%3D%3D</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1751816490.9815</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1751816490</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704391029\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1473109091 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>mega_fly_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G1VIqro5hQY5OOAvy7nMXqA9Dw3r8cz7EWCEno2q</span>\"\n  \"<span class=sf-dump-key>dosshr_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>booking_cookie_agreement_enable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473109091\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-518001745 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 06 Jul 2025 15:41:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkN0QzNndEtEYjJCQ0ZsTE9BUnozR2c9PSIsInZhbHVlIjoiQ3Boc3pVWllNRFJ4MkNmejNuQ09wZERSdE93WHczOUVtSDVzaVY3T29vZnl5OTBrTk9wZjBZTGliNUNua1FEeFNMWnlxUDU0ZWsySzZ2V3dxbDVkVzVpQjJYZVRsZGJaS29LamdZSXdtWHA0ZUlKRHZDQjhNOURZTXN0QlNmNXgiLCJtYWMiOiJmMjk0ZjA0MzE1OWMyNmNiZTg4NzI3M2I2OTM1MTFmZjVkYmFiNTA2OGJhMWFmYWRmYzEwNTEyNzAyYTQyNjM0IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:41:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">mega_fly_session=eyJpdiI6InBpU0t3V0ZLSDZEV3o3cUJ1QXFwTHc9PSIsInZhbHVlIjoiaXJmVWZuSTY3NjZuK2xrdUJQSkI3TnRpclJPV1hEVDdKQ2lKL0k3NVlmOCtIMWN2YVNUWkJOV2tyeUdrYkkweEZvUzA0dGliMmVnSlBoQ3pOTUNyajhCOVhPdkgrUDNDQjJURlRpNHBiSDFzYXhXYkxORlgrM2FPSmVhMzNwSTMiLCJtYWMiOiIyYjgwY2RkNmMxYTc4OTNhZTI4YjA0MzYxZjZmMzgyY2YzNzNiYmQ4OGQ1YmY5OWZiMjVkMDNjY2QxODQ1ZDM0IiwidGFnIjoiIn0%3D; expires=Sun, 06 Jul 2025 17:41:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkN0QzNndEtEYjJCQ0ZsTE9BUnozR2c9PSIsInZhbHVlIjoiQ3Boc3pVWllNRFJ4MkNmejNuQ09wZERSdE93WHczOUVtSDVzaVY3T29vZnl5OTBrTk9wZjBZTGliNUNua1FEeFNMWnlxUDU0ZWsySzZ2V3dxbDVkVzVpQjJYZVRsZGJaS29LamdZSXdtWHA0ZUlKRHZDQjhNOURZTXN0QlNmNXgiLCJtYWMiOiJmMjk0ZjA0MzE1OWMyNmNiZTg4NzI3M2I2OTM1MTFmZjVkYmFiNTA2OGJhMWFmYWRmYzEwNTEyNzAyYTQyNjM0IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:41:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">mega_fly_session=eyJpdiI6InBpU0t3V0ZLSDZEV3o3cUJ1QXFwTHc9PSIsInZhbHVlIjoiaXJmVWZuSTY3NjZuK2xrdUJQSkI3TnRpclJPV1hEVDdKQ2lKL0k3NVlmOCtIMWN2YVNUWkJOV2tyeUdrYkkweEZvUzA0dGliMmVnSlBoQ3pOTUNyajhCOVhPdkgrUDNDQjJURlRpNHBiSDFzYXhXYkxORlgrM2FPSmVhMzNwSTMiLCJtYWMiOiIyYjgwY2RkNmMxYTc4OTNhZTI4YjA0MzYxZjZmMzgyY2YzNzNiYmQ4OGQ1YmY5OWZiMjVkMDNjY2QxODQ1ZDM0IiwidGFnIjoiIn0%3D; expires=Sun, 06-Jul-2025 17:41:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518001745\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1935505333 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c77kQmg5vkfzd0GVNopzYnVCPNyz8Njaodo4OceI</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8001/support/chat/messages</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935505333\", {\"maxDepth\":0})</script>\n"}}