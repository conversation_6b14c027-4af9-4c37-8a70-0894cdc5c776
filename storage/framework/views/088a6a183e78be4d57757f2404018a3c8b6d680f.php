<?php
// Handle multiway index for multi-city trips
$segmentIndex = isset($multiway_index) ? $multiway_index : '';
if (empty($inputName)) {
    $inputName = $segmentIndex !== '' ? 'to_where[' . $segmentIndex . ']' : 'to_where';
}
?>
<?php
    use App\Models\Airports;
    $airports = Airports::all();
?>
<?php
$location_list = $airports;
$location_name = '';
$list_json = [];
$traverse = function ($locations, $prefix = '') use (&$traverse, &$list_json, &$location_name, $inputName) {
    foreach ($locations as $location) {
        if (Request::query($inputName) == $location['code']) {
            $location_name = $location['name'];
        }
        $list_json[] = [
            'id' => $location['code'],
            'title' => $prefix . ' ' . $location['name'] . ' - ' . $location['code'],
        ];
    }
};
$traverse($location_list);
?>
<div class="smart-search">
    <input type="text"
        class="smart-search-location flywt-input"
        <?php echo e((empty(setting_item('flight_location_search_style')) or setting_item('flight_location_search_style') == 'normal') ? 'readonly' : ''); ?>

        placeholder="<?php echo e(__('To')); ?>"
        value="<?php echo e($location_name); ?>"
        data-onLoad="<?php echo e(__('Loading...')); ?>"
        data-default="<?php echo e(json_encode($list_json)); ?>">
    <input type="hidden" class="child_id" name="<?php echo e($inputName); ?>"
        value="<?php echo e(Request::query($inputName)); ?>">
</div>
<?php /**PATH /Users/<USER>/Projects/Mega-Fly/themes/Mytravel/Flight/Views/frontend/layouts/search/fields/to-where-flight.blade.php ENDPATH**/ ?>