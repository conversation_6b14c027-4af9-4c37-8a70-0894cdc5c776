<?php $__env->startPush('css'); ?>
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<?php $__env->stopPush(); ?>

<?php
// Handle multiway index for multi-city trips
$segmentIndex = isset($multiway_index) ? $multiway_index : '';
$inputName = $segmentIndex !== '' ? 'start_oneway[' . $segmentIndex . ']' : 'start_oneway';
$inputClass = $segmentIndex !== '' ? 'single-date-picker-' . $segmentIndex : 'single-date-picker';
$requestKey = $segmentIndex !== '' ? 'start_oneway.' . $segmentIndex : 'start_oneway';
?>

<input type="text"
       class="flywt-input <?php echo e($inputClass); ?>"
       name="<?php echo e($inputName); ?>"
       value="<?php echo e(old($inputName, request($requestKey, date('d/m/Y')))); ?>"
       placeholder="<?php echo e(__('Select date')); ?>"
       autocomplete="off"
       readonly>



<?php $__env->startPush('js'); ?>
    <!-- Flatpickr JS -->
    <script src="//cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Initialize flatpickr for all single date pickers
            flatpickr(".single-date-picker, [class*='single-date-picker-']", {
                    dateFormat: "d/m/Y", // لازم يكون نفس تنسيق القيمة
            });
        });
    </script>
<?php $__env->stopPush(); ?><?php /**PATH /Users/<USER>/Projects/Mega-Fly/themes/Mytravel/Flight/Views/frontend/layouts/search/fields/date-single.blade.php ENDPATH**/ ?>