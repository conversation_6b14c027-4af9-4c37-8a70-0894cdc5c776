<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('themes/mytravel/module/flight/css/flywt-style.css')); ?>">
<?php $__env->stopPush(); ?>

<!-- FlyWT Exact Match Search Container -->
<div class="flywt-search-container">
    <!-- Tab Navigation -->
    <div class="flywt-tabs">
        <button class="flywt-tab <?php echo e(!request()->has('start_roundtrip') && !request()->has('start_multiway') ? 'active' : ''); ?>"
                onclick="showTab('oneway', this)">
            <?php echo e(__('ذهاب فقط')); ?>

        </button>
        <button class="flywt-tab <?php echo e(request()->has('start_roundtrip') ? 'active' : ''); ?>"
                onclick="showTab('roundtrip', this)">
            <?php echo e(__('ذهاب وعودة')); ?>

        </button>
        <button class="flywt-tab <?php echo e(request()->has('start_multiway') ? 'active' : ''); ?>"
                onclick="showTab('multiway', this)">
            <?php echo e(__('مدن متعددة')); ?>

        </button>
    </div>

    <!-- Form Content -->
    <div class="flywt-form-content">
        <!-- One Way Form -->
        <div id="oneway-form" class="flywt-form-tab <?php echo e(!request()->has('start_roundtrip') && !request()->has('start_multiway') ? 'active' : ''); ?>">
            <form action="<?php echo e(route('flight.search')); ?>" method="get">
                <div class="flywt-form-row">
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('من')); ?></label>
                        <?php echo $__env->make('Flight::frontend.layouts.search.fields.from-where-flight', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('الى')); ?></label>
                        <?php echo $__env->make('Flight::frontend.layouts.search.fields.to-where-flight', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('مغادرة')); ?></label>
                        <?php echo $__env->make('Flight::frontend.layouts.search.fields.date-single', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('مسافرين, المقصورة / الدرجة')); ?></label>
                        <div class="flywt-passenger-selector" id="passenger-selector-ow">
                            <div class="flywt-passenger-display">
                                <span id="passenger-text-ow">1 <?php echo e(__('مسافرين')); ?> <?php echo e(__('الدرجة السياحية')); ?></span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </div>
                            <div class="flywt-passenger-dropdown" id="passenger-dropdown-ow">
                                <h6 style="margin-bottom: 15px; color: #333; font-weight: 600;"><?php echo e(__('مسافرون')); ?></h6>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الكبار')); ?> 12+ <?php echo e(__('العمر')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('adults-ow', -1)">-</button>
                                        <span class="flywt-counter-value" id="adults-ow">1</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('adults-ow', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الأطفال')); ?> 2-11 <?php echo e(__('سنة')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('children-ow', -1)">-</button>
                                        <span class="flywt-counter-value" id="children-ow">0</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('children-ow', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الرضع')); ?> 0-2 <?php echo e(__('سنوات')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('infants-ow', -1)">-</button>
                                        <span class="flywt-counter-value" id="infants-ow">0</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('infants-ow', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-class-selector">
                                    <div class="flywt-class-options">
                                        <button type="button" class="flywt-class-option active" data-class="economy"><?php echo e(__('الدرجة السياحية')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="premium"><?php echo e(__('الدرجة الاقتصادية الممتازة')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="business"><?php echo e(__('درجة رجال الأعمال')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="first"><?php echo e(__('الدرجة الأولى')); ?></button>
                                    </div>
                                </div>

                                <button type="button" class="flywt-confirm-btn" onclick="confirmPassengerSelection('ow')"><?php echo e(__('تأكيد الاختيار')); ?></button>
                            </div>
                        </div>
                    </div>

                    <!-- Search Button -->
                    <button type="submit" class="flywt-search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="flywt-options">
                    <label class="flywt-checkbox">
                        <input type="checkbox" name="direct_flight" value="1">
                        <?php echo e(__('رحلة مباشرة')); ?>

                    </label>
                </div>

                <!-- Hidden inputs for passenger data -->
                <input type="hidden" name="adults" id="adults-input-ow" value="1">
                <input type="hidden" name="children" id="children-input-ow" value="0">
                <input type="hidden" name="infants" id="infants-input-ow" value="0">
                <input type="hidden" name="seat_type" id="seat-type-input-ow" value="economy">
            </form>
        </div>

        <!-- Round Trip Form -->
        <div id="roundtrip-form" class="flywt-form-tab <?php echo e(request()->has('start_roundtrip') ? 'active' : ''); ?>">
            <form action="<?php echo e(route('flight.search')); ?>" method="get">
                <div class="flywt-form-row">
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('من')); ?></label>
                        <?php echo $__env->make('Flight::frontend.layouts.search.fields.from-where-flight', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('الى')); ?></label>
                        <?php echo $__env->make('Flight::frontend.layouts.search.fields.to-where-flight', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('مغادرة')); ?></label>
                        <?php echo $__env->make('Flight::frontend.layouts.search.fields.date-single', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('العوده')); ?></label>
                        <input type="text" class="flywt-input single-date-picker-return" name="start_roundtrip"
                               value="<?php echo e(old('start_roundtrip', request('start_roundtrip', date('d/m/Y', strtotime('+1 day'))))); ?>"
                               placeholder="<?php echo e(__('Select return date')); ?>" autocomplete="off" readonly>
                    </div>
                </div>

                <div class="flywt-form-row">
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('مسافرين, المقصورة / الدرجة')); ?></label>
                        <div class="flywt-passenger-selector" id="passenger-selector-rt">
                            <div class="flywt-passenger-display">
                                <span id="passenger-text-rt">1 <?php echo e(__('مسافرين')); ?> <?php echo e(__('الدرجة السياحية')); ?></span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </div>
                            <div class="flywt-passenger-dropdown" id="passenger-dropdown-rt">
                                <h6 style="margin-bottom: 15px; color: #333; font-weight: 600;"><?php echo e(__('مسافرون')); ?></h6>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الكبار')); ?> 12+ <?php echo e(__('العمر')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('adults-rt', -1)">-</button>
                                        <span class="flywt-counter-value" id="adults-rt">1</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('adults-rt', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الأطفال')); ?> 2-11 <?php echo e(__('سنة')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('children-rt', -1)">-</button>
                                        <span class="flywt-counter-value" id="children-rt">0</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('children-rt', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الرضع')); ?> 0-2 <?php echo e(__('سنوات')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('infants-rt', -1)">-</button>
                                        <span class="flywt-counter-value" id="infants-rt">0</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('infants-rt', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-class-selector">
                                    <div class="flywt-class-options">
                                        <button type="button" class="flywt-class-option active" data-class="economy"><?php echo e(__('الدرجة السياحية')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="premium"><?php echo e(__('الدرجة الاقتصادية الممتازة')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="business"><?php echo e(__('درجة رجال الأعمال')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="first"><?php echo e(__('الدرجة الأولى')); ?></button>
                                    </div>
                                </div>

                                <button type="button" class="flywt-confirm-btn" onclick="confirmPassengerSelection('rt')"><?php echo e(__('تأكيد الاختيار')); ?></button>
                            </div>
                        </div>
                    </div>

                    <!-- Search Button -->
                    <button type="submit" class="flywt-search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="flywt-options">
                    <label class="flywt-checkbox">
                        <input type="checkbox" name="direct_flight" value="1">
                        <?php echo e(__('رحلة مباشرة')); ?>

                    </label>
                </div>

                <!-- Hidden inputs for passenger data -->
                <input type="hidden" name="adults" id="adults-input-rt" value="1">
                <input type="hidden" name="children" id="children-input-rt" value="0">
                <input type="hidden" name="infants" id="infants-input-rt" value="0">
                <input type="hidden" name="seat_type" id="seat-type-input-rt" value="economy">
            </form>
        </div>

        <!-- Multi-City Form -->
        <div id="multiway-form" class="flywt-form-tab <?php echo e(request()->has('start_multiway') ? 'active' : ''); ?>">
            <form action="<?php echo e(route('flight.search')); ?>" method="get">
                <div id="multi-city-segments">
                    <!-- Segment 1 -->
                    <div class="flywt-segment" data-segment="0">
                        <div class="flywt-segment-header">
                            <h6 class="flywt-segment-title"><?php echo e(__('من')); ?></h6>
                        </div>
                        <div class="flywt-form-row">
                            <div class="flywt-input-group">
                                <label class="flywt-label"><?php echo e(__('من')); ?></label>
                                <?php echo $__env->make('Flight::frontend.layouts.search.fields.from-where-flight', ['multiway_index' => 0], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="flywt-input-group">
                                <label class="flywt-label"><?php echo e(__('الى')); ?></label>
                                <?php echo $__env->make('Flight::frontend.layouts.search.fields.to-where-flight', ['multiway_index' => 0], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="flywt-input-group">
                                <label class="flywt-label"><?php echo e(__('مغادرة')); ?></label>
                                <?php echo $__env->make('Flight::frontend.layouts.search.fields.date-single', ['multiway_index' => 0], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Segment 2 -->
                    <div class="flywt-segment" data-segment="1">
                        <div class="flywt-segment-header">
                            <h6 class="flywt-segment-title"><?php echo e(__('الى')); ?></h6>
                        </div>
                        <div class="flywt-form-row">
                            <div class="flywt-input-group">
                                <label class="flywt-label"><?php echo e(__('من')); ?></label>
                                <?php echo $__env->make('Flight::frontend.layouts.search.fields.from-where-flight', ['multiway_index' => 1], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="flywt-input-group">
                                <label class="flywt-label"><?php echo e(__('الى')); ?></label>
                                <?php echo $__env->make('Flight::frontend.layouts.search.fields.to-where-flight', ['multiway_index' => 1], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div class="flywt-input-group">
                                <label class="flywt-label"><?php echo e(__('مغادرة')); ?></label>
                                <?php echo $__env->make('Flight::frontend.layouts.search.fields.date-single', ['multiway_index' => 1], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="button" class="flywt-add-segment" onclick="addMultiCitySegment()">
                    <?php echo e(__('أضف مدينة +')); ?>

                </button>

                <div class="flywt-form-row">
                    <div class="flywt-input-group">
                        <label class="flywt-label"><?php echo e(__('مسافرين, المقصورة / الدرجة')); ?></label>
                        <div class="flywt-passenger-selector" id="passenger-selector-mc">
                            <div class="flywt-passenger-display">
                                <span id="passenger-text-mc">1 <?php echo e(__('مسافرين')); ?> <?php echo e(__('الدرجة السياحية')); ?></span>
                                <i class="fas fa-chevron-down dropdown-arrow"></i>
                            </div>
                            <div class="flywt-passenger-dropdown" id="passenger-dropdown-mc">
                                <h6 style="margin-bottom: 15px; color: #333; font-weight: 600;"><?php echo e(__('مسافرون')); ?></h6>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الكبار')); ?> 12+ <?php echo e(__('العمر')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('adults-mc', -1)">-</button>
                                        <span class="flywt-counter-value" id="adults-mc">1</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('adults-mc', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الأطفال')); ?> 2-11 <?php echo e(__('سنة')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('children-mc', -1)">-</button>
                                        <span class="flywt-counter-value" id="children-mc">0</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('children-mc', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-passenger-row">
                                    <div class="flywt-passenger-info">
                                        <h6><?php echo e(__('الرضع')); ?> 0-2 <?php echo e(__('سنوات')); ?></h6>
                                    </div>
                                    <div class="flywt-counter">
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('infants-mc', -1)">-</button>
                                        <span class="flywt-counter-value" id="infants-mc">0</span>
                                        <button type="button" class="flywt-counter-btn" onclick="changeCount('infants-mc', 1)">+</button>
                                    </div>
                                </div>

                                <div class="flywt-class-selector">
                                    <div class="flywt-class-options">
                                        <button type="button" class="flywt-class-option active" data-class="economy"><?php echo e(__('الدرجة السياحية')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="premium"><?php echo e(__('الدرجة الاقتصادية الممتازة')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="business"><?php echo e(__('درجة رجال الأعمال')); ?></button>
                                        <button type="button" class="flywt-class-option" data-class="first"><?php echo e(__('الدرجة الأولى')); ?></button>
                                    </div>
                                </div>

                                <button type="button" class="flywt-confirm-btn" onclick="confirmPassengerSelection('mc')"><?php echo e(__('تأكيد الاختيار')); ?></button>
                            </div>
                        </div>
                    </div>

                    <!-- Search Button -->
                    <button type="submit" class="flywt-search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="flywt-options">
                    <label class="flywt-checkbox">
                        <input type="checkbox" name="direct_flight" value="1">
                        <?php echo e(__('رحلة مباشرة')); ?>

                    </label>
                </div>

                <!-- Hidden inputs for passenger data -->
                <input type="hidden" name="adults" id="adults-input-mc" value="1">
                <input type="hidden" name="children" id="children-input-mc" value="0">
                <input type="hidden" name="infants" id="infants-input-mc" value="0">
                <input type="hidden" name="seat_type" id="seat-type-input-mc" value="economy">
                <input type="hidden" name="start_multiway" value="1">
            </form>
        </div>
    </div>
</div>
<?php $__env->startPush('js'); ?>
<script>
// FlyWT Functionality JavaScript
function showTab(tabName, clickedButton) {
    console.log('Switching to tab:', tabName);

    // Hide all tabs with fade effect
    document.querySelectorAll('.flywt-form-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.flywt-tab').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    const targetTab = document.getElementById(tabName + '-form');
    if (targetTab) {
        // Small delay for smooth transition
        setTimeout(() => {
            targetTab.classList.add('active');
        }, 50);
        console.log('Activated tab:', tabName + '-form');
    } else {
        console.error('Tab not found:', tabName + '-form');
    }

    // Add active class to clicked button
    const buttonToActivate = clickedButton || event.target;
    if (buttonToActivate) {
        buttonToActivate.classList.add('active');
        console.log('Activated button:', buttonToActivate);
    }
}

// Initialize tabs on page load
document.addEventListener('DOMContentLoaded', function() {
    // Ensure at least one tab is active
    const activeTabs = document.querySelectorAll('.flywt-form-tab.active');
    const activeButtons = document.querySelectorAll('.flywt-tab.active');

    if (activeTabs.length === 0) {
        // Default to oneway tab
        const onewayTab = document.getElementById('oneway-form');
        const onewayButton = document.querySelector('.flywt-tab[onclick*="oneway"]');

        if (onewayTab) onewayTab.classList.add('active');
        if (onewayButton) onewayButton.classList.add('active');
    }
});

// Passenger counter functionality
function changeCount(elementId, change) {
    const element = document.getElementById(elementId);
    let currentValue = parseInt(element.textContent);
    let newValue = currentValue + change;

    // Prevent negative values and set minimum adults to 1
    if (elementId.includes('adults') && newValue < 1) {
        newValue = 1;
    } else if (!elementId.includes('adults') && newValue < 0) {
        newValue = 0;
    }

    // Set maximum limits
    if (newValue > 9) {
        newValue = 9;
    }

    element.textContent = newValue;
    updatePassengerDisplay(elementId);
}

// Update passenger display text
function updatePassengerDisplay(elementId) {
    const suffix = elementId.split('-')[1]; // ow, rt, or mc
    const adults = parseInt(document.getElementById('adults-' + suffix).textContent);
    const children = parseInt(document.getElementById('children-' + suffix).textContent);
    const infants = parseInt(document.getElementById('infants-' + suffix).textContent);

    const total = adults + children + infants;
    const activeClass = document.querySelector('#passenger-dropdown-' + suffix + ' .flywt-class-option.active');
    const className = activeClass ? activeClass.textContent.trim() : 'الدرجة السياحية';

    let passengerText = total + ' مسافرين ' + className;
    document.getElementById('passenger-text-' + suffix).textContent = passengerText;
}

// Class selection functionality
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.flywt-class-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from siblings
            this.parentNode.querySelectorAll('.flywt-class-option').forEach(sibling => {
                sibling.classList.remove('active');
            });

            // Add active class to clicked option
            this.classList.add('active');

            // Update display
            const dropdownId = this.closest('.flywt-passenger-dropdown').id;
            const suffix = dropdownId.split('-')[2]; // ow, rt, or mc
            updatePassengerDisplay('adults-' + suffix);
        });
    });

    // Passenger selector toggle
    document.querySelectorAll('.flywt-passenger-selector').forEach(selector => {
        const display = selector.querySelector('.flywt-passenger-display');
        const dropdown = selector.querySelector('.flywt-passenger-dropdown');

        display.addEventListener('click', function() {
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.flywt-passenger-selector')) {
            document.querySelectorAll('.flywt-passenger-dropdown').forEach(dropdown => {
                dropdown.style.display = 'none';
            });
        }
    });
});

// Confirm passenger selection
function confirmPassengerSelection(suffix) {
    const adults = parseInt(document.getElementById('adults-' + suffix).textContent);
    const children = parseInt(document.getElementById('children-' + suffix).textContent);
    const infants = parseInt(document.getElementById('infants-' + suffix).textContent);
    const activeClass = document.querySelector('#passenger-dropdown-' + suffix + ' .flywt-class-option.active');
    const seatType = activeClass ? activeClass.getAttribute('data-class') : 'economy';

    // Update hidden inputs
    document.getElementById('adults-input-' + suffix).value = adults;
    document.getElementById('children-input-' + suffix).value = children;
    document.getElementById('infants-input-' + suffix).value = infants;
    document.getElementById('seat-type-input-' + suffix).value = seatType;

    // Close dropdown
    document.getElementById('passenger-dropdown-' + suffix).style.display = 'none';
}

// Multi-city functionality
let segmentCount = 2;

function addMultiCitySegment() {
    if (segmentCount >= 6) return; // Maximum 6 segments

    const segmentsContainer = document.getElementById('multi-city-segments');
    const newSegment = document.createElement('div');
    newSegment.className = 'flywt-segment';
    newSegment.setAttribute('data-segment', segmentCount);

    newSegment.innerHTML = `
        <div class="flywt-segment-header">
            <h6 class="flywt-segment-title">رحلة ${segmentCount + 1}</h6>
            <button type="button" class="flywt-remove-segment" onclick="removeMultiCitySegment(${segmentCount})">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="flywt-form-row">
            <div class="flywt-input-group">
                <label class="flywt-label">من</label>
                <input type="text" class="flywt-input smart-search-location" name="multi_city[${segmentCount}][from_where]" placeholder="اختر المدينة أو المطار">
            </div>
            <div class="flywt-input-group">
                <label class="flywt-label">الى</label>
                <input type="text" class="flywt-input smart-search-location" name="multi_city[${segmentCount}][to_where]" placeholder="اختر المدينة أو المطار">
            </div>
            <div class="flywt-input-group">
                <label class="flywt-label">مغادرة</label>
                <input type="text" class="flywt-input single-date-picker" name="multi_city[${segmentCount}][start_date]" placeholder="اختر التاريخ" readonly>
            </div>
        </div>
    `;

    segmentsContainer.appendChild(newSegment);
    segmentCount++;

    // Initialize smart search for new inputs
    initializeSmartSearch();
    initializeDatePickers();
}

function removeMultiCitySegment(segmentIndex) {
    const segment = document.querySelector(`[data-segment="${segmentIndex}"]`);
    if (segment) {
        segment.remove();
    }
}

// Initialize smart search and date pickers
function initializeSmartSearch() {
    // The smart search is automatically initialized by home.js
    // We just need to trigger a re-initialization for new elements
    if (typeof $ !== 'undefined' && typeof $.fn.bravoAutocomplete === 'function') {
        // Re-initialize only new smart-search-location elements that haven't been initialized
        $('.smart-search-location').each(function() {
            var $this = $(this);
            var $parent = $this.closest('.smart-search');

            // Check if already initialized (has bravo-autocomplete dropdown)
            if ($parent.find('.bravo-autocomplete').length === 0) {
                var default_list = [];
                try {
                    var dataDefault = $this.attr('data-default');
                    if (dataDefault) {
                        default_list = JSON.parse(dataDefault);
                    }
                } catch (e) {
                    default_list = [];
                }

                var options = {
                    url: bookingCore.url + '/location/search/searchForSelect2',
                    dataDefault: default_list,
                    textLoading: $this.attr("data-onLoad") || "Loading...",
                    iconItem: "icofont-location-pin",
                };

                $this.bravoAutocomplete(options);
            }
        });
    }
}

function initializeDatePickers() {
    // Re-initialize date pickers for new elements
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.single-date-picker', {
            dateFormat: 'd/m/Y',
            minDate: 'today'
        });

        flatpickr('.single-date-picker-return', {
            dateFormat: 'd/m/Y',
            minDate: 'today'
        });
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeSmartSearch();
    initializeDatePickers();
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH /Users/<USER>/Projects/Mega-Fly/themes/Mytravel/Flight/Views/frontend/layouts/search/form-search.blade.php ENDPATH**/ ?>