/* FlyWT Style Flight Search Form */

/* Main Container */
.enhanced-flight-search-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin: 20px 0;
}

.search-form-header {
    margin-bottom: 25px;
}

.search-title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 8px;
}

.search-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
}

/* FlyWT Style Tabs */
.flywt-tabs-wrapper {
    margin-bottom: 25px;
    border-bottom: 1px solid #e5e5e5;
}

.flywt-tabs {
    border: none;
    justify-content: flex-start;
    gap: 0;
}

.flywt-tabs .nav-item {
    margin-bottom: 0;
}

.flywt-tab {
    background: none;
    border: none;
    border-radius: 0;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.flywt-tab:hover {
    color: #007bff;
    background: none;
    border-color: transparent;
}

.flywt-tab.active {
    color: #007bff;
    background: none;
    border-bottom-color: #007bff;
    font-weight: 600;
}

/* Form Container */
.flywt-form-container {
    background: #ffffff;
    border-radius: 8px;
    padding: 25px;
}

/* Input Fields - FlyWT Style */
.flywt-input-group {
    margin-bottom: 20px;
    position: relative;
}

.flywt-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.flywt-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    font-size: 16px;
    color: #333;
    background: #ffffff;
    transition: all 0.3s ease;
}

.flywt-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.flywt-input::placeholder {
    color: #999;
    font-size: 15px;
}

/* Passenger Selector - FlyWT Style */
.flywt-passenger-selector {
    position: relative;
}

.flywt-passenger-btn {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    background: #ffffff;
    text-align: left;
    font-size: 16px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flywt-passenger-btn:hover {
    border-color: #007bff;
}

.flywt-passenger-btn:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.flywt-passenger-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    padding: 20px;
    margin-top: 5px;
    display: none;
}

.flywt-passenger-dropdown.show {
    display: block;
}

/* Passenger Counter */
.flywt-passenger-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.flywt-passenger-row:last-child {
    border-bottom: none;
}

.flywt-passenger-info h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.flywt-passenger-info small {
    color: #666;
    font-size: 13px;
}

.flywt-counter {
    display: flex;
    align-items: center;
    gap: 15px;
}

.flywt-counter-btn {
    width: 35px;
    height: 35px;
    border: 2px solid #e5e5e5;
    border-radius: 50%;
    background: #ffffff;
    color: #666;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.flywt-counter-btn:hover {
    border-color: #007bff;
    color: #007bff;
}

.flywt-counter-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.flywt-counter-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    min-width: 20px;
    text-align: center;
}

/* Class Selector */
.flywt-class-selector {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.flywt-class-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 10px;
}

.flywt-class-option {
    padding: 10px 15px;
    border: 2px solid #e5e5e5;
    border-radius: 6px;
    background: #ffffff;
    cursor: pointer;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.flywt-class-option:hover {
    border-color: #007bff;
    color: #007bff;
}

.flywt-class-option.active {
    border-color: #007bff;
    background: #007bff;
    color: #ffffff;
}

/* Search Button - FlyWT Style */
.flywt-search-btn {
    width: 100%;
    padding: 15px 25px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 25px;
}

.flywt-search-btn:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
}

.flywt-search-btn:active {
    transform: translateY(0);
}

/* Direct Flight Checkbox */
.flywt-checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.flywt-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #007bff;
}

.flywt-checkbox-label {
    font-size: 15px;
    color: #666;
    cursor: pointer;
}

/* Multi-City Segments */
.flywt-segment {
    background: #f8f9fa;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    position: relative;
}

.flywt-segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.flywt-segment-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.flywt-remove-segment {
    background: #dc3545;
    border: none;
    border-radius: 6px;
    color: #ffffff;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.flywt-remove-segment:hover {
    background: #c82333;
}

.flywt-add-segment {
    background: #28a745;
    border: none;
    border-radius: 8px;
    color: #ffffff;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.flywt-add-segment:hover {
    background: #218838;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-flight-search-container {
        padding: 20px;
        margin: 10px;
        border-radius: 8px;
    }
    
    .search-title {
        font-size: 24px;
    }
    
    .flywt-tab {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .flywt-class-options {
        grid-template-columns: 1fr;
    }
    
    .flywt-passenger-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .flywt-counter {
        align-self: flex-end;
    }
}
