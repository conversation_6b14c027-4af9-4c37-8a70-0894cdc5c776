/* FlyWT Exact Match Styling */

/* Main Search Container */
.flywt-search-container {
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    backdrop-filter: none;
    overflow: visible;
    margin: 20px auto;
    padding: 20px;
    max-width: 1000px;
    position: relative;
}

/* Tab Navigation - Aligned to Image */
.flywt-tabs {
    display: flex;
    background: transparent;
    margin: 0 0 15px 0;
    padding: 0;
    border-bottom: none;
    gap: 4px;
    justify-content: flex-start;
}

.flywt-tab {
    background: rgba(255, 255, 255, 0.8);
    border: none;
    padding: 10px 18px;
    font-size: 13px;
    font-weight: 500;
    color: #666666;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    position: relative;
    font-family: inherit;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    min-width: auto;
}

.flywt-tab:hover {
    color: #ff6b35;
    text-decoration: none;
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
}

.flywt-tab.active {
    background: #ff6b35;
    color: #ffffff;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(255, 107, 53, 0.25);
}

/* Form Content */
.flywt-form-content {
    padding: 20px 0 0 0;
    background: transparent;
}

/* Responsive Design - Prevent Overlapping */
@media (max-width: 768px) {
    .flywt-form-row {
        flex-direction: column;
        height: auto;
        padding: 15px;
        border-radius: 20px;
    }

    .flywt-input-group {
        width: 100%;
        min-width: auto;
        border-right: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        padding: 15px 20px;
    }

    .flywt-input-group:last-child {
        border-bottom: none;
        padding-bottom: 60px; /* Space for search button */
    }

    .flywt-search-btn {
        position: absolute;
        bottom: 10px;
        right: 50%;
        transform: translateX(50%);
        top: auto;
    }
}

/* Form Tabs */
.flywt-form-tab {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.flywt-form-tab.active {
    display: block;
    opacity: 1;
}

/* Form Row - Fixed Padding and Overlapping */
.flywt-form-row {
    display: flex;
    align-items: center;
    gap: 0;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50px;
    padding: 8px 70px 8px 8px; /* Right padding for search button */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(15px);
    position: relative;
    min-height: 60px;
    overflow: hidden;
}

/* Input Groups - Fixed Overlapping */
.flywt-input-group {
    flex: 1;
    min-width: 180px;
    position: relative;
    padding: 12px 20px;
    border-right: 1px solid rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 44px;
    box-sizing: border-box;
}

.flywt-input-group:last-child {
    border-right: none;
    padding-right: 20px; /* Normal padding, space handled by form row */
}

.flywt-input-group:first-child {
    padding-left: 24px;
    border-radius: 50px 0 0 50px;
}

.flywt-input-group:last-child {
    border-radius: 0 50px 50px 0;
}

.flywt-label {
    display: block;
    font-size: 10px;
    font-weight: 500;
    color: #999;
    margin-bottom: 2px;
    text-transform: none;
    letter-spacing: 0;
    line-height: 1;
}

/* Input Fields - Aligned to Image */
.flywt-input {
    width: 100%;
    height: auto;
    padding: 0;
    border: none;
    border-radius: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    background: transparent;
    transition: all 0.3s ease;
    box-sizing: border-box;
    font-family: inherit;
    line-height: 1.2;
}

.flywt-input:focus {
    outline: none;
    border: none;
    box-shadow: none;
}

.flywt-input::placeholder {
    color: #bbb;
    font-size: 14px;
    font-weight: 400;
}

/* Smart Search Styling */
.smart-search {
    position: relative;
    width: 100%;
}

.smart-search .flywt-input {
    cursor: pointer;
}

.smart-search .bravo-autocomplete {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
}

.smart-search .bravo-autocomplete.show {
    display: block;
}

.smart-search .bravo-autocomplete .list-item {
    max-height: 250px;
    overflow-y: auto;
}

.smart-search .bravo-autocomplete .item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
    color: #333;
}

.smart-search .bravo-autocomplete .item:hover {
    background-color: #f8f9fa;
}

.smart-search .bravo-autocomplete .item:last-child {
    border-bottom: none;
}

/* Passenger Selector - Fixed Overlapping */
.flywt-passenger-selector {
    position: relative;
    cursor: pointer;
    width: 100%;
    z-index: 5;
}

.flywt-passenger-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: auto;
    padding: 0;
    border: none;
    border-radius: 0;
    background: transparent;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    width: 100%;
}

.flywt-passenger-display:hover {
    color: #ff6b35;
}

.flywt-passenger-display:focus {
    outline: none;
    color: #ff6b35;
}

.flywt-passenger-display .dropdown-arrow {
    font-size: 12px;
    color: #666;
    transition: transform 0.3s ease;
}

.flywt-passenger-selector.open .dropdown-arrow {
    transform: rotate(180deg);
}

/* Passenger Dropdown */
.flywt-passenger-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 2px solid #e5e5e5;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    padding: 20px;
    display: none;
}

.flywt-passenger-dropdown.show {
    display: block;
}

.flywt-passenger-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.flywt-passenger-row:last-child {
    border-bottom: none;
}

.flywt-passenger-info h6 {
    margin: 0 0 2px 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.flywt-passenger-info small {
    font-size: 12px;
    color: #666;
}

.flywt-counter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.flywt-counter-btn {
    width: 36px;
    height: 36px;
    border: 2px solid #e5e5e5;
    border-radius: 50%;
    background: #ffffff;
    color: #666;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.flywt-counter-btn:hover {
    border-color: #007bff;
    color: #007bff;
}

.flywt-counter-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.flywt-counter-value {
    min-width: 30px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

/* Class Selector */
.flywt-class-selector {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.flywt-class-options {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.flywt-class-option {
    padding: 10px 15px;
    border: 2px solid #e5e5e5;
    border-radius: 6px;
    background: #ffffff;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 90px;
}

.flywt-class-option:hover {
    border-color: #007bff;
    color: #007bff;
}

.flywt-class-option.active {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
}

/* Confirm Button */
.flywt-confirm-btn {
    width: 100%;
    padding: 12px 20px;
    background: #007bff;
    color: #ffffff;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.flywt-confirm-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}



/* Multi-City Segments */
.flywt-segment {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 15px;
    position: relative;
}

.flywt-segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.flywt-segment-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.flywt-remove-segment {
    background: #dc3545;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.flywt-remove-segment:hover {
    background: #c82333;
}

.flywt-add-segment {
    background: #28a745;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin: 15px 0;
    width: 100%;
}

.flywt-add-segment:hover {
    background: #218838;
}

/* Additional Options */
.flywt-options {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.flywt-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.flywt-checkbox input[type="checkbox"] {
    margin: 0;
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .flywt-search-container {
        margin: 10px;
        border-radius: 6px;
    }

    .flywt-form-content {
        padding: 20px 15px;
    }

    .flywt-form-row {
        flex-direction: column;
        gap: 15px;
    }

    .flywt-input-group {
        min-width: auto;
    }

    .flywt-tab {
        padding: 12px 15px;
        font-size: 13px;
    }

    .flywt-passenger-dropdown {
        padding: 15px;
    }

    .flywt-class-options {
        justify-content: center;
    }

    .flywt-class-option {
        flex: 1;
        min-width: 70px;
        font-size: 11px;
        padding: 6px 8px;
    }

    .flywt-counter {
        align-self: flex-end;
    }

    .flywt-segment {
        padding: 15px;
        margin-bottom: 15px;
    }

    .flywt-class-selector-segment .flywt-class-option {
        min-width: 100px;
        font-size: 11px;
        padding: 6px 8px;
    }

    .flywt-add-segment {
        width: 100%;
        margin: 15px 0;
    }
}

/* Form Container */
.flywt-form-container {
    background: #ffffff;
    border-radius: 8px;
    padding: 25px;
}




.flywt-passenger-btn {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    background: #ffffff;
    text-align: left;
    font-size: 16px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flywt-passenger-btn:hover {
    border-color: #007bff;
}

.flywt-passenger-btn:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}



/* Passenger Counter */
.flywt-passenger-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.flywt-passenger-row:last-child {
    border-bottom: none;
}

.flywt-passenger-info h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.flywt-passenger-info small {
    color: #666;
    font-size: 13px;
}

.flywt-counter {
    display: flex;
    align-items: center;
    gap: 15px;
}

.flywt-counter-btn {
    width: 35px;
    height: 35px;
    border: 2px solid #e5e5e5;
    border-radius: 50%;
    background: #ffffff;
    color: #666;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.flywt-counter-btn:hover {
    border-color: #007bff;
    color: #007bff;
}

.flywt-counter-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.flywt-counter-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    min-width: 20px;
    text-align: center;
}

/* Class Selector */
.flywt-class-selector {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.flywt-class-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 10px;
}

.flywt-class-option {
    padding: 10px 15px;
    border: 2px solid #e5e5e5;
    border-radius: 6px;
    background: #ffffff;
    cursor: pointer;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.flywt-class-option:hover {
    border-color: #007bff;
    color: #007bff;
}

.flywt-class-option.active {
    border-color: #007bff;
    background: #007bff;
    color: #ffffff;
}

/* Search Button - Fixed Positioning */
.flywt-search-btn {
    background: #ff6b35;
    border: none;
    border-radius: 50%;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 52px;
    height: 52px;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    box-shadow: 0 3px 12px rgba(255, 107, 53, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    flex-shrink: 0;
}

.flywt-search-btn:hover {
    background: #e55a2b;
    transform: translateY(-50%) translateY(-2px);
    box-shadow: 0 5px 18px rgba(255, 107, 53, 0.4);
}

.flywt-search-btn:active {
    transform: translateY(-50%) translateY(0);
}

/* Direct Flight Checkbox */
.flywt-checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
}

.flywt-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #007bff;
}

.flywt-checkbox-label {
    font-size: 15px;
    color: #666;
    cursor: pointer;
}

/* Multi-City Segments */
.flywt-segment {
    background: #f8f9fa;
    border: 2px solid #e5e5e5;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    position: relative;
}

.flywt-segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.flywt-segment-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.flywt-remove-segment {
    background: #dc3545;
    border: none;
    border-radius: 6px;
    color: #ffffff;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.flywt-remove-segment:hover {
    background: #c82333;
}

.flywt-add-segment {
    background: #28a745;
    border: none;
    border-radius: 8px;
    color: #ffffff;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.flywt-add-segment:hover {
    background: #218838;
}

/* Multi-City Class Selector for Segments */
.flywt-class-selector-segment {
    margin-top: 10px;
}

.flywt-class-selector-segment .flywt-class-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.flywt-class-selector-segment .flywt-class-option {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
    min-width: 120px;
}

.flywt-class-selector-segment .flywt-class-option:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.flywt-class-selector-segment .flywt-class-option.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-flight-search-container {
        padding: 20px;
        margin: 10px;
        border-radius: 8px;
    }
    
    .search-title {
        font-size: 24px;
    }
    
    .flywt-tab {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .flywt-class-options {
        grid-template-columns: 1fr;
    }
    
    .flywt-passenger-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .flywt-counter {
        align-self: flex-end;
    }

    .flywt-segment {
        padding: 15px;
        margin-bottom: 15px;
    }

    .flywt-class-selector-segment .flywt-class-option {
        min-width: 100px;
        font-size: 11px;
        padding: 6px 8px;
    }

    .flywt-add-segment {
        width: 100%;
        margin: 15px 0;
    }
}
