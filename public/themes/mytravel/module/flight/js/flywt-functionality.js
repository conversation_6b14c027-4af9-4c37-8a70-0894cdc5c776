// FlyWT Style Flight Search Functionality

document.addEventListener('DOMContentLoaded', function() {
    
    // Passenger Selector Functionality
    initializePassengerSelector();
    
    // Class Selector Functionality
    initializeClassSelector();
    
    // Counter Functionality
    initializeCounters();
    
});

function initializePassengerSelector() {
    // Handle passenger selector dropdown for all forms
    const passengerButtons = document.querySelectorAll('[id^="passenger-selector-"]');
    
    passengerButtons.forEach(button => {
        const formType = button.id.split('-').pop(); // ow, rt, or mc
        const dropdown = document.getElementById(`passenger-dropdown-${formType}`);
        
        if (button && dropdown) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Close other dropdowns
                document.querySelectorAll('.flywt-passenger-dropdown').forEach(dd => {
                    if (dd !== dropdown) {
                        dd.classList.remove('show');
                    }
                });
                
                // Toggle current dropdown
                dropdown.classList.toggle('show');
            });
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.flywt-passenger-selector')) {
            document.querySelectorAll('.flywt-passenger-dropdown').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });
}

function initializeClassSelector() {
    // Handle class selection for all forms
    const classOptions = document.querySelectorAll('.flywt-class-option');
    
    classOptions.forEach(option => {
        option.addEventListener('click', function() {
            const container = this.closest('.flywt-passenger-dropdown');
            const formType = container.id.split('-').pop(); // ow, rt, or mc
            
            // Remove active class from siblings
            container.querySelectorAll('.flywt-class-option').forEach(opt => {
                opt.classList.remove('active');
            });
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Update hidden input
            const classInput = document.getElementById(`class-input-${formType}`);
            if (classInput) {
                classInput.value = this.dataset.class;
            }
            
            // Update display
            updatePassengerDisplay(formType);
        });
    });
}

function initializeCounters() {
    // Handle counter buttons for all forms
    const counterButtons = document.querySelectorAll('.flywt-counter-btn');
    
    counterButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const action = this.dataset.action;
            const target = this.dataset.target;
            const counterElement = document.getElementById(target);
            const formType = target.split('-').pop(); // ow, rt, or mc
            
            if (counterElement) {
                let currentValue = parseInt(counterElement.textContent);
                let newValue = currentValue;
                
                if (action === 'increase') {
                    // Set maximum limits
                    const maxLimits = {
                        adults: 9,
                        children: 8,
                        infants: 8
                    };
                    
                    const passengerType = target.split('-')[0];
                    const maxLimit = maxLimits[passengerType] || 9;
                    
                    if (currentValue < maxLimit) {
                        newValue = currentValue + 1;
                    }
                } else if (action === 'decrease') {
                    // Adults minimum is 1, others minimum is 0
                    const minValue = target.includes('adults') ? 1 : 0;
                    if (currentValue > minValue) {
                        newValue = currentValue - 1;
                    }
                }
                
                // Update counter display
                counterElement.textContent = newValue;
                
                // Update hidden input
                const inputType = target.split('-')[0]; // adults, children, infants
                const hiddenInput = document.getElementById(`${inputType}-input-${formType}`);
                if (hiddenInput) {
                    hiddenInput.value = newValue;
                }
                
                // Update passenger display
                updatePassengerDisplay(formType);
                
                // Update button states
                updateCounterButtonStates(formType);
            }
        });
    });
}

function updatePassengerDisplay(formType) {
    const adultsCount = parseInt(document.getElementById(`adults-${formType}`).textContent);
    const childrenCount = parseInt(document.getElementById(`children-${formType}`).textContent);
    const infantsCount = parseInt(document.getElementById(`infants-${formType}`).textContent);
    
    const totalPassengers = adultsCount + childrenCount + infantsCount;
    
    // Get selected class
    const activeClass = document.querySelector(`#passenger-dropdown-${formType} .flywt-class-option.active`);
    const className = activeClass ? activeClass.textContent.trim() : 'Economy Class';
    
    // Create passenger text
    let passengerText = '';
    if (totalPassengers === 1) {
        passengerText = `1 ${getTranslation('Passenger')}`;
    } else {
        passengerText = `${totalPassengers} ${getTranslation('Passengers')}`;
    }
    
    // Update display
    const displayElement = document.getElementById(`passenger-display-${formType}`);
    if (displayElement) {
        displayElement.textContent = `${passengerText}, ${className}`;
    }
}

function updateCounterButtonStates(formType) {
    // Update adults counter buttons
    const adultsCount = parseInt(document.getElementById(`adults-${formType}`).textContent);
    const adultsDecreaseBtn = document.querySelector(`[data-target="adults-${formType}"][data-action="decrease"]`);
    const adultsIncreaseBtn = document.querySelector(`[data-target="adults-${formType}"][data-action="increase"]`);
    
    if (adultsDecreaseBtn) {
        adultsDecreaseBtn.disabled = adultsCount <= 1;
    }
    if (adultsIncreaseBtn) {
        adultsIncreaseBtn.disabled = adultsCount >= 9;
    }
    
    // Update children counter buttons
    const childrenCount = parseInt(document.getElementById(`children-${formType}`).textContent);
    const childrenDecreaseBtn = document.querySelector(`[data-target="children-${formType}"][data-action="decrease"]`);
    const childrenIncreaseBtn = document.querySelector(`[data-target="children-${formType}"][data-action="increase"]`);
    
    if (childrenDecreaseBtn) {
        childrenDecreaseBtn.disabled = childrenCount <= 0;
    }
    if (childrenIncreaseBtn) {
        childrenIncreaseBtn.disabled = childrenCount >= 8;
    }
    
    // Update infants counter buttons
    const infantsCount = parseInt(document.getElementById(`infants-${formType}`).textContent);
    const infantsDecreaseBtn = document.querySelector(`[data-target="infants-${formType}"][data-action="decrease"]`);
    const infantsIncreaseBtn = document.querySelector(`[data-target="infants-${formType}"][data-action="increase"]`);
    
    if (infantsDecreaseBtn) {
        infantsDecreaseBtn.disabled = infantsCount <= 0;
    }
    if (infantsIncreaseBtn) {
        infantsIncreaseBtn.disabled = infantsCount >= 8;
    }
}

function closePassengerDropdown(formType) {
    const dropdown = document.getElementById(`passenger-dropdown-${formType}`);
    if (dropdown) {
        dropdown.classList.remove('show');
    }
}

function getTranslation(key) {
    // Simple translation function - can be enhanced with actual translations
    const translations = {
        'Passenger': 'Passenger',
        'Passengers': 'Passengers',
        'Economy Class': 'Economy Class',
        'Premium Economy': 'Premium Economy',
        'Business Class': 'Business Class',
        'First Class': 'First Class'
    };
    
    return translations[key] || key;
}

// Multi-city functionality
function addMultiCitySegment() {
    // This will be implemented when we work on multi-city form
    console.log('Add multi-city segment functionality');
}

function removeMultiCitySegment(segmentIndex) {
    // This will be implemented when we work on multi-city form
    console.log('Remove multi-city segment functionality', segmentIndex);
}

// Form validation
function validateFlightSearchForm(formType) {
    let isValid = true;
    const form = document.getElementById(`${formType}-form`);
    
    if (!form) return false;
    
    // Validate required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });
    
    return isValid;
}

// Initialize form validation on submit
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.flywt-form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const formType = this.id.split('-')[0]; // oneway, roundtrip, multiway
            if (!validateFlightSearchForm(formType)) {
                e.preventDefault();
                // Show error message
                console.log('Form validation failed');
            }
        });
    });
});
