// FlyWT Style Flight Search Functionality

document.addEventListener('DOMContentLoaded', function() {

    // Passenger Selector Functionality
    initializePassengerSelector();

    // Class Selector Functionality
    initializeClassSelector();

    // Counter Functionality
    initializeCounters();

    // Multi-city Functionality
    initializeMultiCity();

});

function initializePassengerSelector() {
    // Handle passenger selector dropdown for all forms
    const passengerButtons = document.querySelectorAll('[id^="passenger-selector-"]');
    
    passengerButtons.forEach(button => {
        const formType = button.id.split('-').pop(); // ow, rt, or mc
        const dropdown = document.getElementById(`passenger-dropdown-${formType}`);
        
        if (button && dropdown) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Close other dropdowns
                document.querySelectorAll('.flywt-passenger-dropdown').forEach(dd => {
                    if (dd !== dropdown) {
                        dd.classList.remove('show');
                    }
                });
                
                // Toggle current dropdown
                dropdown.classList.toggle('show');
            });
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.flywt-passenger-selector')) {
            document.querySelectorAll('.flywt-passenger-dropdown').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });
}

function initializeClassSelector() {
    // Handle class selection for all forms
    const classOptions = document.querySelectorAll('.flywt-class-option');
    
    classOptions.forEach(option => {
        option.addEventListener('click', function() {
            const container = this.closest('.flywt-passenger-dropdown');
            const formType = container.id.split('-').pop(); // ow, rt, or mc
            
            // Remove active class from siblings
            container.querySelectorAll('.flywt-class-option').forEach(opt => {
                opt.classList.remove('active');
            });
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Update hidden input
            const classInput = document.getElementById(`class-input-${formType}`);
            if (classInput) {
                classInput.value = this.dataset.class;
            }
            
            // Update display
            updatePassengerDisplay(formType);
        });
    });
}

function initializeCounters() {
    // Handle counter buttons for all forms
    const counterButtons = document.querySelectorAll('.flywt-counter-btn');

    counterButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const action = this.dataset.action;
            const target = this.dataset.target;
            const counterElement = document.getElementById(target);
            const formType = target.split('-').pop(); // ow, rt, or mc

            if (counterElement) {
                let currentValue = parseInt(counterElement.textContent);
                let newValue = currentValue;

                if (action === 'increase') {
                    // Set maximum limits
                    const maxLimits = {
                        adults: 9,
                        children: 8,
                        infants: 8
                    };

                    const passengerType = target.split('-')[0];
                    const maxLimit = maxLimits[passengerType] || 9;

                    if (currentValue < maxLimit) {
                        newValue = currentValue + 1;
                    }
                } else if (action === 'decrease') {
                    // Adults minimum is 1, others minimum is 0
                    const minValue = target.includes('adults') ? 1 : 0;
                    if (currentValue > minValue) {
                        newValue = currentValue - 1;
                    }
                }

                // Update counter display
                counterElement.textContent = newValue;

                // Update hidden input
                const inputType = target.split('-')[0]; // adults, children, infants
                const hiddenInput = document.getElementById(`${inputType}-input-${formType}`);
                if (hiddenInput) {
                    hiddenInput.value = newValue;
                }

                // Update passenger display
                updatePassengerDisplay(formType);

                // Update button states
                updateCounterButtonStates(formType);
            }
        });
    });
}

function initializeMultiCity() {
    // Initialize add segment button
    const addButton = document.getElementById('add-multiway-segment');
    if (addButton) {
        addButton.addEventListener('click', addMultiCitySegment);
    }

    // Initialize existing segment class selectors
    initializeSegmentClassSelectors();

    // Update add button visibility
    updateAddButtonVisibility();
}

function updatePassengerDisplay(formType) {
    const adultsCount = parseInt(document.getElementById(`adults-${formType}`).textContent);
    const childrenCount = parseInt(document.getElementById(`children-${formType}`).textContent);
    const infantsCount = parseInt(document.getElementById(`infants-${formType}`).textContent);
    
    const totalPassengers = adultsCount + childrenCount + infantsCount;
    
    // Get selected class
    const activeClass = document.querySelector(`#passenger-dropdown-${formType} .flywt-class-option.active`);
    const className = activeClass ? activeClass.textContent.trim() : 'Economy Class';
    
    // Create passenger text
    let passengerText = '';
    if (totalPassengers === 1) {
        passengerText = `1 ${getTranslation('Passenger')}`;
    } else {
        passengerText = `${totalPassengers} ${getTranslation('Passengers')}`;
    }
    
    // Update display
    const displayElement = document.getElementById(`passenger-display-${formType}`);
    if (displayElement) {
        displayElement.textContent = `${passengerText}, ${className}`;
    }
}

function updateCounterButtonStates(formType) {
    // Update adults counter buttons
    const adultsCount = parseInt(document.getElementById(`adults-${formType}`).textContent);
    const adultsDecreaseBtn = document.querySelector(`[data-target="adults-${formType}"][data-action="decrease"]`);
    const adultsIncreaseBtn = document.querySelector(`[data-target="adults-${formType}"][data-action="increase"]`);
    
    if (adultsDecreaseBtn) {
        adultsDecreaseBtn.disabled = adultsCount <= 1;
    }
    if (adultsIncreaseBtn) {
        adultsIncreaseBtn.disabled = adultsCount >= 9;
    }
    
    // Update children counter buttons
    const childrenCount = parseInt(document.getElementById(`children-${formType}`).textContent);
    const childrenDecreaseBtn = document.querySelector(`[data-target="children-${formType}"][data-action="decrease"]`);
    const childrenIncreaseBtn = document.querySelector(`[data-target="children-${formType}"][data-action="increase"]`);
    
    if (childrenDecreaseBtn) {
        childrenDecreaseBtn.disabled = childrenCount <= 0;
    }
    if (childrenIncreaseBtn) {
        childrenIncreaseBtn.disabled = childrenCount >= 8;
    }
    
    // Update infants counter buttons
    const infantsCount = parseInt(document.getElementById(`infants-${formType}`).textContent);
    const infantsDecreaseBtn = document.querySelector(`[data-target="infants-${formType}"][data-action="decrease"]`);
    const infantsIncreaseBtn = document.querySelector(`[data-target="infants-${formType}"][data-action="increase"]`);
    
    if (infantsDecreaseBtn) {
        infantsDecreaseBtn.disabled = infantsCount <= 0;
    }
    if (infantsIncreaseBtn) {
        infantsIncreaseBtn.disabled = infantsCount >= 8;
    }
}

function closePassengerDropdown(formType) {
    const dropdown = document.getElementById(`passenger-dropdown-${formType}`);
    if (dropdown) {
        dropdown.classList.remove('show');
    }
}

function getTranslation(key) {
    // Simple translation function - can be enhanced with actual translations
    const translations = {
        'Passenger': 'Passenger',
        'Passengers': 'Passengers',
        'Economy Class': 'Economy Class',
        'Premium Economy': 'Premium Economy',
        'Business Class': 'Business Class',
        'First Class': 'First Class'
    };
    
    return translations[key] || key;
}

// Multi-city functionality
let segmentCount = 2;
const maxSegments = 5;

function addMultiCitySegment() {
    if (segmentCount >= maxSegments) {
        alert('Maximum 5 flights allowed');
        return;
    }

    const segmentsContainer = document.getElementById('multiway-segments');
    if (!segmentsContainer) return;

    const newSegmentHTML = `
        <div class="flywt-segment" data-segment="${segmentCount}">
            <div class="flywt-segment-header">
                <h6 class="flywt-segment-title">Flight ${segmentCount + 1}</h6>
                <button type="button" class="flywt-remove-segment" onclick="removeMultiCitySegment(${segmentCount})">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="row">
                <!-- From Field -->
                <div class="col-md-4">
                    <div class="flywt-input-group">
                        <label class="flywt-label">From</label>
                        <div class="smart-search">
                            <input type="text" class="smart-search-location flywt-input" placeholder="From" name="from_where[${segmentCount}]">
                            <input type="hidden" class="child_id" name="from_where[${segmentCount}]">
                        </div>
                    </div>
                </div>

                <!-- To Field -->
                <div class="col-md-4">
                    <div class="flywt-input-group">
                        <label class="flywt-label">To</label>
                        <div class="smart-search">
                            <input type="text" class="smart-search-location flywt-input" placeholder="To" name="to_where[${segmentCount}]">
                            <input type="hidden" class="child_id" name="to_where[${segmentCount}]">
                        </div>
                    </div>
                </div>

                <!-- Departure Date -->
                <div class="col-md-4">
                    <div class="flywt-input-group">
                        <label class="flywt-label">Departure</label>
                        <input type="text" class="flywt-input single-date-picker-${segmentCount}" name="start_oneway[${segmentCount}]" placeholder="Select date" readonly>
                    </div>
                </div>

                <!-- Seat Type for each segment -->
                <div class="col-md-12">
                    <div class="flywt-input-group">
                        <label class="flywt-label">Class for this flight</label>
                        <div class="flywt-class-selector-segment">
                            <div class="flywt-class-options">
                                <div class="flywt-class-option active" data-class="economy" data-segment="${segmentCount}">Economy Class</div>
                                <div class="flywt-class-option" data-class="premium" data-segment="${segmentCount}">Premium Economy</div>
                                <div class="flywt-class-option" data-class="business" data-segment="${segmentCount}">Business Class</div>
                                <div class="flywt-class-option" data-class="first" data-segment="${segmentCount}">First Class</div>
                            </div>
                        </div>
                        <!-- Hidden input for seat type -->
                        <input type="hidden" name="seat_type[${segmentCount}]" id="seat-type-${segmentCount}" value="economy">
                    </div>
                </div>
            </div>
        </div>
    `;

    segmentsContainer.insertAdjacentHTML('beforeend', newSegmentHTML);
    segmentCount++;

    // Initialize flatpickr for the new date field
    if (typeof flatpickr !== 'undefined') {
        flatpickr(`.single-date-picker-${segmentCount - 1}`, {
            dateFormat: "d/m/Y"
        });
    }

    // Re-initialize class selectors for the new segment
    initializeSegmentClassSelectors();

    // Update add button visibility
    updateAddButtonVisibility();
}

function removeMultiCitySegment(segmentIndex) {
    const segment = document.querySelector(`[data-segment="${segmentIndex}"]`);
    if (segment && segmentCount > 2) {
        segment.remove();
        segmentCount--;
        updateAddButtonVisibility();
        renumberSegments();
    }
}

function initializeSegmentClassSelectors() {
    // Handle class selection for segment-specific selectors
    const segmentClassOptions = document.querySelectorAll('.flywt-class-selector-segment .flywt-class-option');

    segmentClassOptions.forEach(option => {
        option.addEventListener('click', function() {
            const segmentIndex = this.dataset.segment;
            const container = this.closest('.flywt-class-selector-segment');

            // Remove active class from siblings in this segment
            container.querySelectorAll('.flywt-class-option').forEach(opt => {
                opt.classList.remove('active');
            });

            // Add active class to clicked option
            this.classList.add('active');

            // Update hidden input for this segment
            const seatTypeInput = document.getElementById(`seat-type-${segmentIndex}`);
            if (seatTypeInput) {
                seatTypeInput.value = this.dataset.class;
            }
        });
    });
}

function updateAddButtonVisibility() {
    const addButton = document.getElementById('add-multiway-segment');
    if (addButton) {
        addButton.style.display = segmentCount >= maxSegments ? 'none' : 'block';
    }
}

function renumberSegments() {
    const segments = document.querySelectorAll('.flywt-segment');
    segments.forEach((segment, index) => {
        const title = segment.querySelector('.flywt-segment-title');
        if (title) {
            title.textContent = `Flight ${index + 1}`;
        }
        segment.setAttribute('data-segment', index);
    });
}

// Form validation
function validateFlightSearchForm(formType) {
    let isValid = true;
    const form = document.getElementById(`${formType}-form`);
    
    if (!form) return false;
    
    // Validate required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });
    
    return isValid;
}

// Initialize form validation on submit
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.flywt-form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const formType = this.id.split('-')[0]; // oneway, roundtrip, multiway
            if (!validateFlightSearchForm(formType)) {
                e.preventDefault();
                // Show error message
                console.log('Form validation failed');
            }
        });
    });
});
