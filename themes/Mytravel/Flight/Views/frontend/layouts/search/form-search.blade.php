<!-- Load jQuery before any other scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Load Font Awesome without integrity attribute -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
<!-- FlyWT Style CSS -->
<link rel="stylesheet" href="{{ asset('themes/mytravel/module/flight/css/flywt-style.css') }}"

<!-- Enhanced Search Form Container -->
<div class="enhanced-flight-search-container">
    <div class="search-form-header text-center mb-2">
        <h2 class="search-title mb-1">
            {{ __('Find Your Perfect Flight') }}
        </h2>
        <p class="search-subtitle">{{ __('Search and compare flights from multiple airlines worldwide') }}</p>
    </div>

<div class="col-md-12 mb-3">
    <!-- FlyWT Style Tab Navigation -->
    <div class="flywt-tabs-wrapper">
        <ul class="nav nav-tabs flywt-tabs" id="tripTypeTabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link flywt-tab {{ request()->has('start_oneway') ? 'active' : '' }}"
                   id="oneway-tab"
                   data-toggle="tab"
                   href="#oneway-pane"
                   role="tab"
                   aria-controls="oneway-pane"
                   aria-selected="true">
                    {{ __('One Way') }}
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link flywt-tab {{ request()->has('start_oneway') ? '' : (request()->has('multiway') ? '' : 'active') }}"
                   id="roundtrip-tab"
                   data-toggle="tab"
                   href="#roundtrip-pane"
                   role="tab"
                   aria-controls="roundtrip-pane"
                   aria-selected="false">
                    {{ __('Round Trip') }}
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link flywt-tab {{ request()->has('multiway') ? 'active' : '' }}"
                   id="multiway-tab"
                   data-toggle="tab"
                   href="#multiway-pane"
                   role="tab"
                   aria-controls="multiway-pane"
                   aria-selected="false">
                    {{ __('Multi City') }}
                </a>
            </li>
        </ul>
    </div>
</div>
<div class="col-md-12">
    <!-- FlyWT Style Form Container -->
    <div class="tab-content flywt-form-container" id="tripTypeContent">
        <div class="tab-pane fade {{ request()->has('start_oneway') ? 'show active' : '' }}" id="oneway-pane" role="tabpanel" aria-labelledby="oneway-tab">
            <form action="{{ route("flight.search") }}" class="flywt-form" method="get" id="oneway-form">
                <div class="row">
                    <!-- From Field -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('From') }}</label>
                            @include('Flight::frontend.layouts.search.fields.from-where-flight')
                        </div>
                    </div>

                    <!-- To Field -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('To') }}</label>
                            @include('Flight::frontend.layouts.search.fields.to-where-flight')
                        </div>
                    </div>

                    <!-- Departure Date -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('Departure') }}</label>
                            @include('Flight::frontend.layouts.search.fields.date-single')
                        </div>
                    </div>

                    <!-- Passengers & Class -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('Passengers, Cabin/Class') }}</label>
                            <div class="flywt-passenger-selector">
                                <button type="button" class="flywt-passenger-btn" id="passenger-selector-ow">
                                    <span id="passenger-display-ow">1 {{ __('Passenger') }} {{ __('Economy Class') }}</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="flywt-passenger-dropdown" id="passenger-dropdown-ow">
                                    <!-- Passenger Counters -->
                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Adults') }}</h6>
                                            <small>12+ {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="adults-ow">-</button>
                                            <span class="flywt-counter-value" id="adults-ow">1</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="adults-ow">+</button>
                                        </div>
                                    </div>

                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Children') }}</h6>
                                            <small>2-11 {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="children-ow">-</button>
                                            <span class="flywt-counter-value" id="children-ow">0</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="children-ow">+</button>
                                        </div>
                                    </div>

                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Infants') }}</h6>
                                            <small>0-2 {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="infants-ow">-</button>
                                            <span class="flywt-counter-value" id="infants-ow">0</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="infants-ow">+</button>
                                        </div>
                                    </div>

                                    <!-- Class Selector -->
                                    <div class="flywt-class-selector">
                                        <div class="flywt-class-options">
                                            <div class="flywt-class-option active" data-class="economy">{{ __('Economy Class') }}</div>
                                            <div class="flywt-class-option" data-class="premium">{{ __('Premium Economy') }}</div>
                                            <div class="flywt-class-option" data-class="business">{{ __('Business Class') }}</div>
                                            <div class="flywt-class-option" data-class="first">{{ __('First Class') }}</div>
                                        </div>
                                    </div>

                                    <div class="text-center mt-3">
                                        <button type="button" class="btn btn-primary" onclick="closePassengerDropdown('ow')">{{ __('Confirm Selection') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" name="adults" id="adults-input-ow" value="1">
                <input type="hidden" name="children" id="children-input-ow" value="0">
                <input type="hidden" name="infants" id="infants-input-ow" value="0">
                <input type="hidden" name="seat_type" id="class-input-ow" value="economy">
                <input type="hidden" name="start_oneway" value="1">


                <!-- Direct Flight Option -->
                <div class="flywt-checkbox-wrapper">
                    <input type="checkbox" class="flywt-checkbox" id="direct-flights-ow" name="direct_flight">
                    <label for="direct-flights-ow" class="flywt-checkbox-label">{{ __('Direct Flight') }}</label>
                </div>

                <!-- Search Button -->
                <button type="submit" class="flywt-search-btn">
                    <i class="fas fa-search me-2"></i>
                    {{ __('Search') }}
                </button>
            </form>
        </div>
        <div class="tab-pane fade {{ request()->has('start_oneway') ? '' : (request()->has('multiway') ? '' : 'show active') }}" id="roundtrip-pane" role="tabpanel" aria-labelledby="roundtrip-tab">
            <form action="{{ route('flight.search') }}" class="flywt-form" method="get" id="roundtrip-form">
                <div class="row">
                    <!-- From Field -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('From') }}</label>
                            @include('Flight::frontend.layouts.search.fields.from-where-flight')
                        </div>
                    </div>

                    <!-- To Field -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('To') }}</label>
                            @include('Flight::frontend.layouts.search.fields.to-where-flight')
                        </div>
                    </div>

                    <!-- Departure Date -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('Departure') }}</label>
                            @include('Flight::frontend.layouts.search.fields.date-single')
                        </div>
                    </div>

                    <!-- Return Date -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('Return') }}</label>
                            @include('Flight::frontend.layouts.search.fields.date-single', ['return' => true])
                        </div>
                    </div>

                    <!-- Passengers & Class -->
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('Passengers, Cabin/Class') }}</label>
                            <div class="flywt-passenger-selector">
                                <button type="button" class="flywt-passenger-btn" id="passenger-selector-rt">
                                    <span id="passenger-display-rt">1 {{ __('Passenger') }} {{ __('Economy Class') }}</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="flywt-passenger-dropdown" id="passenger-dropdown-rt">
                                    <!-- Passenger Counters -->
                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Adults') }}</h6>
                                            <small>12+ {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="adults-rt">-</button>
                                            <span class="flywt-counter-value" id="adults-rt">1</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="adults-rt">+</button>
                                        </div>
                                    </div>

                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Children') }}</h6>
                                            <small>2-11 {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="children-rt">-</button>
                                            <span class="flywt-counter-value" id="children-rt">0</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="children-rt">+</button>
                                        </div>
                                    </div>

                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Infants') }}</h6>
                                            <small>0-2 {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="infants-rt">-</button>
                                            <span class="flywt-counter-value" id="infants-rt">0</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="infants-rt">+</button>
                                        </div>
                                    </div>

                                    <!-- Class Selector -->
                                    <div class="flywt-class-selector">
                                        <div class="flywt-class-options">
                                            <div class="flywt-class-option active" data-class="economy">{{ __('Economy Class') }}</div>
                                            <div class="flywt-class-option" data-class="premium">{{ __('Premium Economy') }}</div>
                                            <div class="flywt-class-option" data-class="business">{{ __('Business Class') }}</div>
                                            <div class="flywt-class-option" data-class="first">{{ __('First Class') }}</div>
                                        </div>
                                    </div>

                                    <div class="text-center mt-3">
                                        <button type="button" class="btn btn-primary" onclick="closePassengerDropdown('rt')">{{ __('Confirm Selection') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" name="adults" id="adults-input-rt" value="1">
                <input type="hidden" name="children" id="children-input-rt" value="0">
                <input type="hidden" name="infants" id="infants-input-rt" value="0">
                <input type="hidden" name="seat_type" id="class-input-rt" value="economy">

                <!-- Direct Flight Option -->
                <div class="flywt-checkbox-wrapper">
                    <input type="checkbox" class="flywt-checkbox" id="direct-flights-rt" name="direct_flight">
                    <label for="direct-flights-rt" class="flywt-checkbox-label">{{ __('Direct Flight') }}</label>
                </div>

                <!-- Search Button -->
                <button type="submit" class="flywt-search-btn">
                    <i class="fas fa-search me-2"></i>
                    {{ __('Search') }}
                </button>
            </form>
        </div>
        <div class="tab-pane fade {{ request()->has('multiway') ? 'show active' : '' }}" id="multiway-pane" role="tabpanel" aria-labelledby="multiway-tab">
            <form action="{{ route('flight.search') }}" class="flywt-form" method="get" id="multiway-form">
                <input type="hidden" name="multiway" value="1">

                <!-- Multi-City Segments Container -->
                <div id="multiway-segments">
                    @for ($i = 0; $i < 2; $i++)
                        <div class="flywt-segment" data-segment="{{ $i }}">
                            <div class="flywt-segment-header">
                                <h6 class="flywt-segment-title">{{ __('Flight') }} {{ $i + 1 }}</h6>
                                @if($i > 1)
                                    <button type="button" class="flywt-remove-segment" onclick="removeMultiCitySegment({{ $i }})">
                                        <i class="fas fa-times"></i>
                                    </button>
                                @endif
                            </div>

                            <div class="row">
                                <!-- From Field -->
                                <div class="col-md-4">
                                    <div class="flywt-input-group">
                                        <label class="flywt-label">{{ __('From') }}</label>
                                        @include('Flight::frontend.layouts.search.fields.from-where-flight', ['multiway_index' => $i])
                                    </div>
                                </div>

                                <!-- To Field -->
                                <div class="col-md-4">
                                    <div class="flywt-input-group">
                                        <label class="flywt-label">{{ __('To') }}</label>
                                        @include('Flight::frontend.layouts.search.fields.to-where-flight', ['multiway_index' => $i])
                                    </div>
                                </div>

                                <!-- Departure Date -->
                                <div class="col-md-4">
                                    <div class="flywt-input-group">
                                        <label class="flywt-label">{{ __('Departure') }}</label>
                                        @include('Flight::frontend.layouts.search.fields.date-single', ['multiway_index' => $i])
                                    </div>
                                </div>

                                <!-- Seat Type for each segment -->
                                <div class="col-md-12">
                                    <div class="flywt-input-group">
                                        <label class="flywt-label">{{ __('Class for this flight') }}</label>
                                        <div class="flywt-class-selector-segment">
                                            <div class="flywt-class-options">
                                                <div class="flywt-class-option active" data-class="economy" data-segment="{{ $i }}">{{ __('Economy Class') }}</div>
                                                <div class="flywt-class-option" data-class="premium" data-segment="{{ $i }}">{{ __('Premium Economy') }}</div>
                                                <div class="flywt-class-option" data-class="business" data-segment="{{ $i }}">{{ __('Business Class') }}</div>
                                                <div class="flywt-class-option" data-class="first" data-segment="{{ $i }}">{{ __('First Class') }}</div>
                                            </div>
                                        </div>
                                        <!-- Hidden input for seat type -->
                                        <input type="hidden" name="seat_type[{{ $i }}]" id="seat-type-{{ $i }}" value="economy">
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endfor
                </div>

                <!-- Add City Button -->
                <button type="button" class="flywt-add-segment" id="add-multiway-segment">
                    <i class="fas fa-plus me-2"></i>
                    {{ __('Add City') }} +
                </button>

                <!-- Passengers & Class (Global for all segments) -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="flywt-input-group">
                            <label class="flywt-label">{{ __('Passengers, Cabin/Class') }}</label>
                            <div class="flywt-passenger-selector">
                                <button type="button" class="flywt-passenger-btn" id="passenger-selector-mc">
                                    <span id="passenger-display-mc">1 {{ __('Passenger') }} {{ __('Economy Class') }}</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="flywt-passenger-dropdown" id="passenger-dropdown-mc">
                                    <!-- Passenger Counters -->
                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Adults') }}</h6>
                                            <small>12+ {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="adults-mc">-</button>
                                            <span class="flywt-counter-value" id="adults-mc">1</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="adults-mc">+</button>
                                        </div>
                                    </div>

                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Children') }}</h6>
                                            <small>2-11 {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="children-mc">-</button>
                                            <span class="flywt-counter-value" id="children-mc">0</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="children-mc">+</button>
                                        </div>
                                    </div>

                                    <div class="flywt-passenger-row">
                                        <div class="flywt-passenger-info">
                                            <h6>{{ __('Infants') }}</h6>
                                            <small>0-2 {{ __('years') }}</small>
                                        </div>
                                        <div class="flywt-counter">
                                            <button type="button" class="flywt-counter-btn" data-action="decrease" data-target="infants-mc">-</button>
                                            <span class="flywt-counter-value" id="infants-mc">0</span>
                                            <button type="button" class="flywt-counter-btn" data-action="increase" data-target="infants-mc">+</button>
                                        </div>
                                    </div>

                                    <div class="text-center mt-3">
                                        <button type="button" class="btn btn-primary" onclick="closePassengerDropdown('mc')">{{ __('Confirm Selection') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" name="adults" id="adults-input-mc" value="1">
                <input type="hidden" name="children" id="children-input-mc" value="0">
                <input type="hidden" name="infants" id="infants-input-mc" value="0">

                <!-- Direct Flight Option -->
                <div class="flywt-checkbox-wrapper">
                    <input type="checkbox" class="flywt-checkbox" id="direct-flights-mc" name="direct_flight">
                    <label for="direct-flights-mc" class="flywt-checkbox-label">{{ __('Direct Flight') }}</label>
                </div>

                <!-- Search Button -->
                <button type="submit" class="flywt-search-btn">
                    <i class="fas fa-search me-2"></i>
                    {{ __('Search') }}
                </button>
            </form>
        </div>
    </div>
</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced flight search functionality
    let maxSegments = 5;
    let minSegments = 2;
    let segmentCount = 2;
    const multiwayFields = document.getElementById('multiway-fields');

    // Initialize enhanced features
    initializeSwapButtons();
    initializeFormValidation();
    initializeLoadingStates();
    initializeTabAnimations();

    // Multi-city segment management
    document.getElementById('add-multiway-segment').onclick = function() {
        if (segmentCount >= maxSegments) {
            showNotification('Maximum 5 flights allowed', 'warning');
            return;
        }
        
        let newIndex = segmentCount;
        let segment = document.createElement('div');
        segment.className = 'col-md-12 mb-3 multiway-segment enhanced-segment animate-slide-in';
        segment.setAttribute('data-segment', newIndex);

        // Enhanced segment HTML with better structure
        segment.innerHTML = `
            <div class="segment-header">
                <span class="segment-number">${newIndex + 1}</span>
                <h6 class="segment-title">Flight ${newIndex + 1}</h6>
            </div>
            <div class="row align-items-end g-3">
                <div class="col-lg-3 col-md-6">
                    <label class="enhanced-label">
                        <i class="fas fa-map-marker-alt me-2"></i>From
                    </label>
                    <div class="enhanced-input-wrapper">
                        <div class="item">
                            <span class="d-block text-gray-1 font-weight-normal mb-0 text-left"></span>
                            <div class="mb-4">
                                <div class="input-group border-bottom-1 py-2">
                                    <i class="flaticon-pin-1 d-flex align-items-center mr-2 text-primary font-weight-semi-bold"></i>
                                    <div class="smart-search border-0 p-0 form-control height-40 bg-transparent">
                                        <input type="text" class="smart-search-location parent_text font-weight-bold font-size-16 shadow-none hero-form font-weight-bold border-0 p-0" readonly placeholder="Where are you going?" value="">
                                        <input type="hidden" class="child_id" name="from_where[${newIndex}]" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label class="enhanced-label">
                        <i class="fas fa-map-marker-alt me-2"></i>To
                    </label>
                    <div class="enhanced-input-wrapper">
                        <div class="item">
                            <span class="d-block text-gray-1 font-weight-normal mb-0 text-left"></span>
                            <div class="mb-4">
                                <div class="input-group border-bottom-1 py-2">
                                    <i class="flaticon-pin-1 d-flex align-items-center mr-2 text-primary font-weight-semi-bold"></i>
                                    <div class="smart-search border-0 p-0 form-control height-40 bg-transparent">
                                        <input type="text" class="smart-search-location parent_text font-weight-bold font-size-16 shadow-none hero-form font-weight-bold border-0 p-0" readonly placeholder="Where are you going?" value="">
                                        <input type="hidden" class="child_id" name="to_where[${newIndex}]" value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label class="enhanced-label">
                        <i class="fas fa-calendar-alt me-2"></i>Date
                    </label>
                    <div class="enhanced-input-wrapper">
                        <div class="item">
                            <span class="d-block text-gray-1 text-left font-weight-normal"></span>
                            <div class="border-bottom-1 mb-4 form-content">
                                <div class="input-group flex-nowrap form-date-search">
                                    <div class="input-group-prepend">
                                        <span class="d-flex align-items-center mr-2">
                                            <i class="flaticon-calendar text-primary font-weight-semi-bold"></i>
                                        </span>
                                    </div>
                                    <input type="text" class="form-control hero-form bg-transparent border-0 flatpickr-input p-0 font-size-16 font-weight-bold single-date-picker-${newIndex}" name="start_oneway[${newIndex}]" value="${new Date().toLocaleDateString('en-GB')}" placeholder="Select date" autocomplete="off">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label class="enhanced-label">
                        <i class="fas fa-chair me-2"></i>Class
                    </label>
                    <div class="enhanced-input-wrapper">
                        <select class="form-select enhanced-select" name="seat_type[${newIndex}]">
                            <option value="economy">Economy</option>
                            <option value="premium">Premium Economy</option>
                            <option value="business">Business</option>
                            <option value="first">First Class</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-2 col-md-12 d-flex align-items-end justify-content-center">
                    <button type="button" class="btn btn-outline-danger btn-sm remove-segment enhanced-remove-btn" title="Remove Flight">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
            </div>
        `;

        multiwayFields.appendChild(segment);

        // Initialize flatpickr for the new date input
        if (typeof flatpickr !== 'undefined') {
            flatpickr(segment.querySelector('.single-date-picker-' + newIndex), {
                dateFormat: "d/m/Y",
                minDate: "today"
            });
        }

        segmentCount++;
        updateRemoveButtons();
        updateAddButtonState();
        
        // Animate the new segment
        setTimeout(() => {
            segment.classList.add('animate-fade-in');
        }, 100);
    };

    function updateRemoveButtons() {
        document.querySelectorAll('.remove-segment').forEach(btn => {
            btn.onclick = function() {
                if (segmentCount <= minSegments) return;
                
                const segment = btn.closest('.multiway-segment');
                segment.classList.add('animate-slide-out');
                
                setTimeout(() => {
                    segment.remove();
                    segmentCount--;
                    updateRemoveButtons();
                    updateAddButtonState();
                    updateSegmentNumbers();
                }, 300);
            };
        });
    }

    function updateAddButtonState() {
        const addBtn = document.getElementById('add-multiway-segment');
        if (segmentCount >= maxSegments) {
            addBtn.disabled = true;
            addBtn.innerHTML = '<i class="fas fa-check me-2"></i>Maximum flights added';
        } else {
            addBtn.disabled = false;
            addBtn.innerHTML = '<i class="fas fa-plus-circle me-2"></i>Add Another Flight';
        }
    }

    function updateSegmentNumbers() {
        document.querySelectorAll('.multiway-segment').forEach((segment, index) => {
            const numberEl = segment.querySelector('.segment-number');
            const titleEl = segment.querySelector('.segment-title');
            if (numberEl) numberEl.textContent = index + 1;
            if (titleEl) titleEl.textContent = `Flight ${index + 1}`;
        });
    }

    function initializeSwapButtons() {
        document.querySelectorAll('.swap-locations-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Add swap functionality here
                btn.classList.add('animate-spin');
                setTimeout(() => btn.classList.remove('animate-spin'), 500);
                showNotification('Locations swapped!', 'success');
            });
        });
    }

    function initializeFormValidation() {
        document.querySelectorAll('.enhanced-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!validateForm(form)) {
                    e.preventDefault();
                    return false;
                }
                
                showLoadingState(form);
            });
        });
    }

    function validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            showNotification('Please fill in all required fields', 'error');
        }

        return isValid;
    }

    function showLoadingState(form) {
        const overlay = form.querySelector('.form-loading-overlay');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (overlay) overlay.style.display = 'flex';
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
        }
    }

    function initializeLoadingStates() {
        // Add loading states for async operations
        document.querySelectorAll('.smart-search-location').forEach(input => {
            input.addEventListener('focus', function() {
                this.closest('.enhanced-input-wrapper').classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.closest('.enhanced-input-wrapper').classList.remove('focused');
            });
        });
    }

    function initializeTabAnimations() {
        document.querySelectorAll('.enhanced-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Add tab switching animation
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.classList.add('animate-fade-in');
                    setTimeout(() => target.classList.remove('animate-fade-in'), 300);
                }
            });
        });
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `enhanced-notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Initialize
    updateRemoveButtons();
    updateAddButtonState();
});
</script>
<style>
/* Enhanced Flight Search Form Styles */
.enhanced-flight-search-container {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    padding: 0.75rem 1rem;
    border-radius: 10px;
    margin-bottom: 0.75rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
}

.enhanced-flight-search-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,0 0,1000 1000,800 1000,0"/></svg>');
    pointer-events: none;
}

.search-form-header {
    position: relative;
    z-index: 2;
}

.title-decoration {
    position: relative;
}

.plane-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin: 0 auto;
}

.plane-icon {
    font-size: 1.5rem;
    color: white;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.search-title {
    color: white;
    font-size: 1.75rem;
    font-weight: 800;
    margin-bottom: 0.25rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.2);
    letter-spacing: -0.5px;
}

.search-subtitle {
    color: rgba(255,255,255,0.9);
    font-size: 0.9rem;
    margin-bottom: 0;
    font-weight: 400;
}

.search-stats {
    margin-top: 1rem;
}

.stat-item {
    padding: 0.5rem;
}

.stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-label {
    display: block;
    font-size: 0.85rem;
    color: rgba(255,255,255,0.8);
    margin-top: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Tab Styles */
.enhanced-tabs {
    background: rgba(255,255,255,0.12);
    backdrop-filter: blur(15px);
    border-radius: 12px;
    padding: 0.4rem;
    border: none;
    position: relative;
    z-index: 2;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.enhanced-tabs .nav-item {
    margin: 0 0.5rem;
}

.enhanced-tab {
    background: transparent !important;
    border: none !important;
    color: rgba(255,255,255,0.8) !important;
    border-radius: 10px !important;
    padding: 0.6rem 1.25rem !important;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.enhanced-tab .tab-content-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.enhanced-tab .tab-icon {
    margin-right: 0.75rem;
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.enhanced-tab .tab-indicator {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 4px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.enhanced-tab:hover {
    background: rgba(255,255,255,0.18) !important;
    color: white !important;
    transform: translateY(-3px);
}

.enhanced-tab:hover .tab-icon {
    transform: scale(1.15);
}

.enhanced-tab.active {
    background: white !important;
    color: #1e40af !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.enhanced-tab.active .tab-indicator {
    width: 100%;
}

/* Enhanced Card Styles */
.enhanced-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 45px rgba(0,0,0,0.12);
    position: relative;
    z-index: 2;
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255,255,255,0.2);
    padding: 1rem !important;
}

/* Enhanced Form Styles */
.enhanced-form {
    position: relative;
}

.enhanced-label {
    display: flex;
    align-items: center;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.4rem;
    font-size: 0.85rem;
}

.enhanced-label i {
    color: #1e40af;
    margin-right: 0.5rem;
}

.required-indicator {
    color: #ef4444;
    margin-left: 0.25rem;
    font-weight: 600;
}

.optional-indicator {
    color: #6b7280;
    font-size: 0.85rem;
    font-weight: 400;
    margin-left: 0.5rem;
}

.enhanced-input-wrapper {
    position: relative;
    margin-bottom: 0.5rem;
}

.enhanced-input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    border-radius: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.enhanced-input-wrapper.focused::before {
    opacity: 1;
}

.enhanced-input-wrapper input,
.enhanced-input-wrapper select {
    border-radius: 10px !important;
    border: 2px solid #e1e8ed !important;
    padding: 0.5rem 0.875rem !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
    background: white !important;
    font-weight: 500 !important;
    height: 38px !important;
    width: 100% !important;
}

.enhanced-input-wrapper input:focus,
.enhanced-input-wrapper select:focus {
    border-color: #1e40af !important;
    box-shadow: 0 0 0 0.25rem rgba(30, 64, 175, 0.25) !important;
    transform: translateY(-2px);
}

.input-helper-text {
    font-size: 0.7rem;
    color: #6b7280;
    margin-top: 0.2rem;
    font-style: italic;
}

.enhanced-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
}

/* Search Options */
.search-options {
    padding: 1rem 0;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.checkbox-wrapper:hover {
    background: rgba(102, 126, 234, 0.1);
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: #1e40af;
    border-color: #1e40af;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: 700;
}

.checkbox-label {
    font-size: 0.9rem;
    color: #4b5563;
    font-weight: 500;
}

/* Swap Button */
.swap-locations-btn {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    background: #1e40af;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
    transition: all 0.3s ease;
    z-index: 10;
}

.swap-locations-btn:hover {
    background: #5a67d8;
    transform: translateY(-50%) scale(1.15);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

/* Enhanced Search Button */
.enhanced-search-btn {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 35px rgba(30, 64, 175, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 45px;
    font-size: 0.95rem;
    font-weight: 600;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.btn-shimmer {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.enhanced-search-btn:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 50px rgba(30, 64, 175, 0.6);
}

.enhanced-search-btn:hover .btn-shimmer {
    left: 100%;
}

.enhanced-search-btn .btn-arrow {
    transition: transform 0.3s ease;
}

.enhanced-search-btn:hover .btn-arrow {
    transform: translateX(8px);
}

/* Suggestion Chips */
.suggestion-chips {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.chip {
    background: rgba(30, 64, 175, 0.1);
    color: #1e40af;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(30, 64, 175, 0.2);
    transition: all 0.3s ease;
}

.chip:hover {
    background: #1e40af;
    color: white;
    transform: translateY(-2px);
}

/* Multi-city Enhancements */
.multiway-header {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-radius: 12px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
}

.header-content {
    max-width: 500px;
    margin: 0 auto;
}

.icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border-radius: 50%;
    margin: 0 auto;
}

.header-icon {
    font-size: 1.5rem;
    color: white;
}

.multiway-header h5 {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.5rem;
}

.multiway-header p {
    font-size: 1rem;
    line-height: 1.6;
}

.enhanced-segment {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-radius: 12px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border: 2px solid #e1e8ed;
    transition: all 0.3s ease;
    position: relative;
}

.enhanced-segment:hover {
    border-color: #1e40af;
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(30, 64, 175, 0.15);
}

.segment-header {
    margin-bottom: 1rem;
}

.segment-number {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    font-size: 1rem;
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.segment-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

.segment-badge {
    background: rgba(30, 64, 175, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 10px;
    border: 1px solid rgba(30, 64, 175, 0.2);
}

.flight-connection {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background: rgba(30, 64, 175, 0.1);
    border-radius: 50%;
    color: #1e40af;
    font-size: 1.2rem;
}

.add-flight-section {
    background: rgba(16, 185, 129, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 2px dashed rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;
}

.add-flight-section:hover {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.enhanced-add-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 15px;
    padding: 1rem 2.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
    font-size: 1rem;
}

.enhanced-add-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
}

.enhanced-add-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.flight-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(30, 64, 175, 0.1);
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    border: 1px solid rgba(30, 64, 175, 0.2);
}

.enhanced-remove-btn {
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.enhanced-remove-btn:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    transform: scale(1.05);
}

/* Loading States */
.form-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 25px;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: #667eea;
}

.loading-spinner i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

/* Search Suggestions */
.search-suggestions {
    opacity: 0.8;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(20px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.animate-slide-out {
    animation: slideOut 0.3s ease-in;
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

.animate-spin {
    animation: spin 0.5s ease-in-out;
}

/* Notifications */
.enhanced-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    font-weight: 600;
    border-left: 4px solid #667eea;
}

.enhanced-notification.success {
    border-left-color: #10b981;
    color: #065f46;
}

.enhanced-notification.error {
    border-left-color: #ef4444;
    color: #991b1b;
}

.enhanced-notification.warning {
    border-left-color: #f59e0b;
    color: #92400e;
}

.enhanced-notification.show {
    transform: translateX(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-flight-search-container {
        padding: 1rem;
        border-radius: 15px;
    }
    
    .search-title {
        font-size: 1.8rem;
    }
    
    .enhanced-tabs {
        flex-direction: column;
        align-items: stretch;
    }
    
    .enhanced-tab {
        margin-bottom: 0.5rem;
        text-align: center;
    }
    
    .enhanced-segment {
        padding: 1rem;
    }
    
    .swap-locations-btn {
        position: static;
        margin: 0.5rem auto;
        transform: none;
    }
    
    .enhanced-search-btn {
        width: 100%;
        margin-top: 1rem;
    }
}

@media (max-width: 576px) {
    .enhanced-flight-search-container {
        margin: 0 -15px;
        border-radius: 0;
    }
    
    .enhanced-card {
        border-radius: 0;
        margin: 0 -15px;
    }
    
    .search-title {
        font-size: 1.5rem;
    }
    
    .search-subtitle {
        font-size: 1rem;
    }
}

/* Form validation styles */
.is-invalid {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25) !important;
}

/* Enhanced focus states */
.enhanced-input-wrapper input:focus,
.enhanced-input-wrapper select:focus {
    outline: none;
}

/* Custom scrollbar for dropdowns */
.custom-select-dropdown::-webkit-scrollbar {
    width: 6px;
}

.custom-select-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-select-dropdown::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 3px;
}

.custom-select-dropdown::-webkit-scrollbar-thumb:hover {
    background: #5a67d8;
}
</style>

<!-- FlyWT Functionality JavaScript -->
<script src="{{ asset('themes/mytravel/module/flight/js/flywt-functionality.js') }}"></script>
