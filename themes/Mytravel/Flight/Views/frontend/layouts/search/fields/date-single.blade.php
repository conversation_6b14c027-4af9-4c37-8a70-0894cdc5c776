@push('css')
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
@endpush

<?php
// Handle multiway index for multi-city trips
$segmentIndex = isset($multiway_index) ? $multiway_index : '';
$inputName = $segmentIndex !== '' ? 'start_oneway[' . $segmentIndex . ']' : 'start_oneway';
$inputClass = $segmentIndex !== '' ? 'single-date-picker-' . $segmentIndex : 'single-date-picker';
$requestKey = $segmentIndex !== '' ? 'start_oneway.' . $segmentIndex : 'start_oneway';
?>

<input type="text"
       class="flywt-input {{ $inputClass }}"
       name="{{ $inputName }}"
       value="{{ old($inputName, request($requestKey, date('d/m/Y'))) }}"
       placeholder="{{ __('Select date') }}"
       autocomplete="off"
       readonly>



@push('js')
    <!-- Flatpickr JS -->
    <script src="//cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Initialize flatpickr for all single date pickers
            flatpickr(".single-date-picker, [class*='single-date-picker-']", {
                    dateFormat: "d/m/Y", // لازم يكون نفس تنسيق القيمة
            });
        });
    </script>
@endpush