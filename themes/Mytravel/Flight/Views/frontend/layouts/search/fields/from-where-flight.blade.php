<?php
// Handle multiway index for multi-city trips
$segmentIndex = isset($multiway_index) ? $multiway_index : '';
if (empty($inputName)) {
    $inputName = $segmentIndex !== '' ? 'from_where[' . $segmentIndex . ']' : 'from_where';
}
?>
@php
    use App\Models\Airports;
    $airports = Airports::all();
@endphp
<?php
$location_list = $airports;
$location_name = '';
$list_json = [];
$traverse = function ($locations, $prefix = '') use (&$traverse, &$list_json, &$location_name, $inputName) {
    foreach ($locations as $location) {
        if (Request::query($inputName) == $location['code']) {
            $location_name = $location['name'];
        }
        $list_json[] = [
            'id' => $location['code'],
            'title' => $prefix . ' ' . $location['name'] . ' - ' . $location['code'],
        ];
    }
};
$traverse($location_list);
?>
<div class="smart-search">
    <input type="text"
        class="smart-search-location flywt-input"
        {{ (empty(setting_item('flight_location_search_style')) or setting_item('flight_location_search_style') == 'normal') ? 'readonly' : '' }}
        placeholder="{{ __('From') }}"
        value="{{ $location_name }}"
        data-onLoad="{{ __('Loading...') }}"
        data-default="{{ json_encode($list_json) }}">
    <input type="hidden" class="child_id" name="{{ $inputName }}"
        value="{{ Request::query($inputName) }}">
</div>
